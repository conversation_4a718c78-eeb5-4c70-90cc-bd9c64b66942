{"name": "@admin/design-tokens", "version": "0.0.0", "private": true, "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}, "./css": "./dist/tokens.css"}, "scripts": {"build": "tsup", "dev": "tsup --watch", "check-types": "tsc --noEmit"}, "devDependencies": {"@admin/typescript-config": "*", "tsup": "^8.0.0", "typescript": "^5.8.2"}, "files": ["dist/**"]}