"use client";

import * as React from "react";
import { <PERSON>evronLef<PERSON>, Loader2 } from "lucide-react";
import { cn } from "./lib/utils";
import { DataForm, FormField } from "./data-form";
import { FadeIn } from "./fade-in";
import { ScaleIn } from "./scale-in";
import { ModuleSelector, ProjectModuleType, ModuleRecommendation, DEFAULT_MODULES } from "./module-selector";
import { ChatBubble } from "./chat-bubble";
import { StreamingChatBubble } from "./streaming-chat-bubble";
import { GuidedConversation, ConversationStep, ConversationMessage } from "./guided-conversation";
import { CatalogSelection, SelectedCatalogItem } from "./catalog-selection";
import { ContextSelection, ContextDataResult } from "./context-selection";

export interface DataCreationModalProps {
  /** Whether the modal is open */
  isOpen: boolean;
  /** Callback to close the modal */
  onClose: () => void;
  /** Entity type being created */
  entityType: string;
  /** Form fields to display */
  fields: FormField[];
  /** Whether the form is in loading state */
  loading?: boolean;
  /** Whether the form submission is disabled */
  submitDisabled?: boolean;
  /** Callback when field value changes */
  onFieldChange: (fieldId: string, value: any) => void;
  /** Callback when form is submitted */
  onSubmit: () => void;
  /** Original command that started this creation */
  originalCommand?: string;
  /** Unknown category notification message */
  unknownCategoryMessage?: string;
  /** AI conversation response message */
  aiMessage?: string;
  /** Whether to show the form or keep in conversation mode */
  showForm?: boolean;
  /** Conversation history for displaying all messages */
  conversationHistory?: ConversationMessage[];
  /** Callback for quick form submission */
  onQuickFormSubmit?: (message: string) => void;
  /** Whether AI is waiting for more information */
  aiNeedsMoreInfo?: boolean;
  /** Whether AI is currently typing/generating response */
  aiIsTyping?: boolean;
  /** Whether to stream the AI response */
  aiIsStreaming?: boolean;
  /** Callback when AI streaming is complete */
  onAiStreamingComplete?: () => void;
  /** Module recommendations for project creation */
  moduleRecommendations?: ModuleRecommendation[];
  /** Selected modules for project creation */
  selectedModules?: ProjectModuleType[];
  /** Module selection method */
  moduleSelectionMethod?: 'manual' | 'ai-recommended' | 'hybrid';
  /** Callback when module selection changes */
  onModuleToggle?: (module: ProjectModuleType, enabled: boolean) => void;
  /** Callback when module selection method changes */
  onModuleSelectionMethodChange?: (method: 'manual' | 'ai-recommended' | 'hybrid') => void;
  /** Whether this is a guided conversation flow */
  isGuidedFlow?: boolean;
  /** Current step in guided conversation */
  currentStep?: string;
  /** All conversation steps */
  conversationSteps?: Array<{ type: string; title: string; isComplete: boolean }>;
  /** Callback when user sends a message in guided flow */
  onGuidedMessage?: (message: string) => void;
  /** Whether to show catalog selection interface */
  showCatalogSelection?: boolean;
  /** Available catalog items for selection */
  catalogItems?: Array<{
    id: string;
    productName: string;
    productDescription: string;
    categoryLabel: 'Product' | 'Service';
    imageSrc?: string;
  }>;
  /** Currently selected catalog items */
  selectedCatalogItems?: SelectedCatalogItem[];
  /** Callback when catalog selection changes */
  onCatalogSelectionChange?: (selectedItems: SelectedCatalogItem[]) => void;
  /** Whether catalog items are loading */
  catalogLoading?: boolean;
  /** Whether to show context selection interface */
  showContextSelection?: boolean;
  /** Context data for selection */
  contextData?: ContextDataResult;
  /** Callback when context selection changes */
  onContextSelectionChange?: (itemId: string, isSelected: boolean, quantity?: number) => void;
  /** Callback when context selection is confirmed */
  onContextSelectionConfirm?: () => void;
  /** Additional CSS classes */
  className?: string;
}

const DataCreationModal = React.forwardRef<HTMLDivElement, DataCreationModalProps>(
  ({
    className,
    isOpen,
    onClose,
    entityType,
    fields,
    loading = false,
    submitDisabled = false,
    onFieldChange,
    onSubmit,
    originalCommand,
    unknownCategoryMessage,
    aiMessage,
    aiNeedsMoreInfo = false,
    aiIsTyping = false,
    aiIsStreaming = false,
    onAiStreamingComplete,
    showForm = true,
    conversationHistory = [],
    onQuickFormSubmit,
    moduleRecommendations = [],
    selectedModules = [],
    moduleSelectionMethod = 'ai-recommended',
    onModuleToggle,
    onModuleSelectionMethodChange,
    isGuidedFlow = false,
    currentStep,
    conversationSteps = [],
    onGuidedMessage,
    showCatalogSelection = false,
    catalogItems = [],
    selectedCatalogItems = [],
    onCatalogSelectionChange,
    catalogLoading = false
  }, ref) => {
    // State for mini-form fields
    const [quickFormData, setQuickFormData] = React.useState<Record<string, string>>({});

    // Handle quick form field changes
    const handleQuickFormChange = (fieldName: string, value: string) => {
      setQuickFormData(prev => ({
        ...prev,
        [fieldName]: value
      }));
    };

    // Analyze AI message to determine what fields to show
    const getRelevantFields = () => {
      if (!aiMessage) return [];

      const message = aiMessage.toLowerCase();
      const fields = [];

      // Get all previous conversation content to check what's already been provided
      const conversationText = conversationHistory
        .map(msg => msg.content.toLowerCase())
        .join(' ');

      // Helper function to check if information was already provided
      const wasAlreadyProvided = (keywords: string[]) => {
        return keywords.some(keyword => {
          // Look for more specific patterns that indicate the info was actually provided
          const patterns = [
            `${keyword}:`,           // "projectName: something"
            `${keyword} is`,         // "project name is something"
            `${keyword} will be`,    // "project name will be something"
            `${keyword}=`,           // "projectName=something"
          ];

          return patterns.some(pattern => conversationText.includes(pattern));
        });
      };

      // Check what the AI is asking for, but exclude already provided info
      if ((message.includes('name') && message.includes('project')) &&
          !wasAlreadyProvided(['projectname'])) {
        fields.push({ key: 'projectName', label: 'Project Name', placeholder: 'e.g., Website Redesign, Mobile App' });
      }

      if (message.includes('client') &&
          !wasAlreadyProvided(['clientname'])) {
        fields.push({ key: 'clientName', label: 'Client Name', placeholder: 'e.g., ABC Corp, John Smith' });
      }

      if (message.includes('description') &&
          !wasAlreadyProvided(['description'])) {
        fields.push({ key: 'description', label: 'Project Description', placeholder: 'e.g., Redesign company website with modern UI' });
      }

      if (message.includes('start date') &&
          !wasAlreadyProvided(['startdate'])) {
        fields.push({ key: 'startDate', label: 'Start Date', placeholder: 'e.g., January 15, 2024' });
      }

      if (message.includes('end date') &&
          !wasAlreadyProvided(['enddate'])) {
        fields.push({ key: 'endDate', label: 'End Date', placeholder: 'e.g., March 15, 2024' });
      }

      if ((message.includes('timeline') || message.includes('duration')) &&
          !wasAlreadyProvided(['timeline'])) {
        fields.push({ key: 'timeline', label: 'Timeline', placeholder: 'e.g., 3 months, 6 weeks' });
      }

      if (message.includes('budget') &&
          !wasAlreadyProvided(['budget'])) {
        fields.push({ key: 'budget', label: 'Budget', placeholder: 'e.g., $50,000' });
      }

      // Employee fields
      if ((message.includes('role') || message.includes('position')) &&
          !wasAlreadyProvided(['role'])) {
        fields.push({ key: 'role', label: 'Role/Position', placeholder: 'e.g., Senior Developer, Manager' });
      }

      if (message.includes('department') &&
          !wasAlreadyProvided(['department'])) {
        fields.push({ key: 'department', label: 'Department', placeholder: 'e.g., Engineering, Sales, Marketing' });
      }

      // Inventory fields
      if ((message.includes('materials') || message.includes('items')) &&
          !wasAlreadyProvided(['itemtype'])) {
        fields.push({ key: 'itemType', label: 'Item Type', placeholder: 'e.g., Steel sheets, Screws, Paint' });
      }

      if ((message.includes('quantity') || message.includes('how many')) &&
          !wasAlreadyProvided(['quantity'])) {
        fields.push({ key: 'quantity', label: 'Quantity', placeholder: 'e.g., 100, 50' });
      }

      return fields;
    };

    // Handle quick form submission
    const handleQuickFormSubmit = () => {
      // Convert quick form data to a natural language message
      const entries = Object.entries(quickFormData).filter(([_, value]) => value.trim() !== '');
      if (entries.length === 0) return;

      const message = entries.map(([key, value]) => `${key}: ${value}`).join(', ');

      // Trigger the message submission
      if (onQuickFormSubmit) {
        onQuickFormSubmit(message);
      }

      // Clear the form
      setQuickFormData({});
    };
    // Calculate if form is valid
    const hasErrors = fields.some(field => field.error);
    const hasRequiredEmpty = fields.some(field => field.required && (!field.value || field.value === ''));
    const isFormValid = !hasErrors && !hasRequiredEmpty;

    return (
      <FadeIn show={isOpen} duration="normal" easing="smooth" unmountOnExit>
        <ScaleIn
          show={isOpen}
          duration="normal"
          easing="smooth"
          initialScale={0.95}
          className={cn(
            "absolute bg-[var(--color-background-primary)] rounded-[var(--radius-8)]",
            "p-[var(--spacing-16)] flex flex-col z-10 transition-modal",
            "top-0 left-0 bottom-0 w-[400px]",
            className
          )}
          style={{
            boxShadow: 'var(--shadow-outer1)',
          }}
        >
          <div ref={ref} className="flex flex-col h-full">
            {/* Header */}
            <div className="flex justify-between items-center mb-[var(--spacing-16)]">
              <div className="space-y-[var(--spacing-4)]">
                <h2 className="text-base font-semibold text-[var(--color-text-primary)]">
                  Data Creation Assistant
                </h2>
                {originalCommand && (
                  <p className="text-xs text-[var(--color-text-secondary)]">
                    "{originalCommand}"
                  </p>
                )}
              </div>
              <button
                onClick={onClose}
                className="text-[var(--color-text-primary)] hover-scale"
                title="Close"
              >
                <ChevronLeft size={16} />
              </button>
            </div>

            {/* Content */}
            <div className="flex-1 overflow-y-auto flex flex-col">
              {/* Guided Conversation for Projects */}
              {isGuidedFlow && entityType === 'project' ? (
                <div className="flex-1 flex flex-col">
                  {/* Guided Conversation */}
                  <div className={showCatalogSelection ? "flex-shrink-0 h-80" : "flex-1"}>
                    <GuidedConversation
                      currentStep={currentStep}
                      steps={conversationSteps}
                      messages={conversationHistory}
                      isTyping={aiIsTyping}
                      isStreaming={aiIsStreaming}
                      streamingMessage={aiMessage}
                      onStreamingComplete={onAiStreamingComplete}
                      onSendMessage={onGuidedMessage || (() => {})}
                      loading={loading}
                    />
                  </div>

                  {/* Catalog Selection Interface for Guided Flow */}
                  <div className="bg-purple-500 text-white p-4 m-2" style={{position: 'fixed', top: '100px', left: '450px', zIndex: 9999, width: '300px'}}>
                    🔍 CATALOG DEBUG:<br/>
                    showCatalogSelection: {showCatalogSelection ? '✅ TRUE' : '❌ FALSE'}<br/>
                    catalogItems: {catalogItems.length}<br/>
                    catalogLoading: {catalogLoading ? '⏳ TRUE' : '✅ FALSE'}<br/>
                    currentStep: {currentStep || 'undefined'}
                  </div>
                  <div className="border-t border-[var(--color-stroke)] p-[var(--spacing-16)] space-y-[var(--spacing-16)]">
                    {showCatalogSelection ? (
                      <div className="space-y-4">
                        <h3 className="text-lg font-semibold">Available Catalog Items ({catalogItems.length})</h3>
                        <div className="grid grid-cols-1 gap-4 max-h-96 overflow-y-auto">
                          {catalogItems.map((item, index) => (
                            <div key={item.id || index} className="border border-gray-300 rounded-lg p-4 bg-white">
                              <div className="flex justify-between items-start">
                                <div className="flex-1">
                                  <h4 className="font-medium text-gray-900">{item.productName}</h4>
                                  <p className="text-sm text-gray-600 mt-1">{item.productDescription}</p>
                                  <span className={`inline-block px-2 py-1 rounded text-xs font-medium mt-2 ${
                                    item.categoryLabel === 'Product'
                                      ? 'bg-blue-100 text-blue-800'
                                      : 'bg-green-100 text-green-800'
                                  }`}>
                                    {item.categoryLabel}
                                  </span>
                                </div>
                                <div className="ml-4">
                                  <input
                                    type="number"
                                    min="0"
                                    placeholder="Qty"
                                    className="w-16 px-2 py-1 border border-gray-300 rounded text-sm"
                                  />
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                        {/* Submit button for catalog selection */}
                        <div className="flex justify-end pt-4">
                          <button
                            onClick={onSubmit}
                            className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
                          >
                            Continue with Selected Items
                          </button>
                        </div>
                      </div>
                    ) : (
                      <div className="text-red-500">Catalog selection is FALSE - should only show during catalog_selection step</div>
                    )}
                  </div>
                </div>
              ) : (
                <div className="flex-1 overflow-y-auto space-y-[var(--spacing-16)]">
                {/* Unknown Category Notification */}
                {unknownCategoryMessage && (
                  <div className="bg-yellow-50 border border-yellow-200 rounded-[var(--radius-4)] p-[var(--spacing-12)]">
                    <div className="flex items-start space-x-[var(--spacing-8)]">
                      <div className="text-yellow-600 text-sm">⚠️</div>
                      <div className="flex-1">
                        <p className="text-sm text-yellow-800 leading-relaxed">
                          {unknownCategoryMessage}
                        </p>
                      </div>
                    </div>
                  </div>
                )}

                {/* AI Conversation Messages */}
                {(conversationHistory.length > 0 || originalCommand || aiMessage) && (
                  <div className="space-y-[var(--spacing-8)]">
                    {/* Conversation History */}
                    {conversationHistory.map((message, index) => {
                      // Don't show the last AI message if we're currently streaming a new one
                      const isLastAiMessage = message.role === 'ai' && index === conversationHistory.length - 1;
                      const shouldShowStreaming = isLastAiMessage && aiIsStreaming && aiMessage;

                      if (shouldShowStreaming) {
                        return (
                          <StreamingChatBubble
                            key={message.id}
                            message={aiMessage}
                            isUser={false}
                            isStreaming={aiIsStreaming}
                            streamingSpeed={25}
                            onStreamingComplete={onAiStreamingComplete}
                            timestamp={message.timestamp}
                            showTimestamp={false}
                          />
                        );
                      }

                      return (
                        <ChatBubble
                          key={message.id}
                          message={message.content}
                          isUser={message.role === 'user'}
                          timestamp={message.timestamp}
                          showTimestamp={false}
                        />
                      );
                    })}

                    {/* Fallback: User's original command (if no conversation history) */}
                    {conversationHistory.length === 0 && originalCommand && (
                      <ChatBubble
                        message={originalCommand}
                        isUser={true}
                        timestamp={new Date()}
                        showTimestamp={false}
                      />
                    )}

                    {/* AI Typing Indicator */}
                    {aiIsTyping && (
                      <div className="flex justify-start">
                        <div className="bg-[var(--color-background-secondary)] px-[var(--spacing-16)] py-[var(--spacing-12)] rounded-[8px] rounded-bl-[2px] shadow-sm">
                          <div className="flex items-center space-x-[var(--spacing-8)]">
                            <Loader2 size={14} className="animate-spin text-[var(--color-text-secondary)]" />
                            <span className="text-sm text-[var(--color-text-secondary)]">AI is thinking...</span>
                          </div>
                        </div>
                      </div>
                    )}

                    {/* New AI Response (when not in history yet) */}
                    {aiMessage && !aiIsStreaming && !conversationHistory.some(msg => msg.content === aiMessage && msg.role === 'ai') && (
                      <ChatBubble
                        message={aiMessage}
                        isUser={false}
                        timestamp={new Date()}
                        showTimestamp={false}
                      />
                    )}

                    {/* AI Question Form - Mini form for answering AI questions (not for guided flows) */}
                    {!isGuidedFlow && !showForm && aiMessage && !aiIsStreaming && !aiIsTyping && aiNeedsMoreInfo && getRelevantFields().length > 0 && (
                      <div className="flex w-full mb-[var(--spacing-12)] justify-start">
                        <div className="max-w-[95%] bg-[var(--color-background-secondary)] text-[var(--color-text-primary)] rounded-[8px] rounded-bl-[2px] shadow-sm">
                          <div className="p-[var(--spacing-16)]">
                            <p className="text-sm leading-relaxed mb-[var(--spacing-12)] text-[var(--color-text-secondary)]">
                              You can answer using the message bar below, or fill out these quick fields:
                            </p>

                            {/* Dynamic quick form fields based on AI message */}
                            <div className="space-y-[var(--spacing-12)]">
                              {getRelevantFields().map((field) => (
                                <div key={field.key}>
                                  <label className="block text-sm font-medium text-[var(--color-text-primary)] mb-[var(--spacing-4)]">
                                    {field.label}
                                  </label>
                                  <input
                                    type={field.key === 'quantity' ? 'number' : 'text'}
                                    value={quickFormData[field.key] || ''}
                                    onChange={(e) => handleQuickFormChange(field.key, e.target.value)}
                                    placeholder={field.placeholder}
                                    className="w-full px-[var(--spacing-12)] py-[var(--spacing-8)] border border-[var(--color-border-primary)] rounded-[var(--radius-4)] text-sm"
                                  />
                                </div>
                              ))}

                              {getRelevantFields().length > 0 && (
                                <button
                                  type="button"
                                  onClick={handleQuickFormSubmit}
                                  disabled={Object.values(quickFormData).every(value => !value || value.trim() === '')}
                                  className="w-full bg-[var(--color-primary)] text-white py-[var(--spacing-8)] px-[var(--spacing-16)] rounded-[var(--radius-4)] text-sm font-medium hover:opacity-90 transition-opacity disabled:opacity-50 disabled:cursor-not-allowed"
                                >
                                  Continue with these details
                                </button>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    )}

                    {/* AI Form Response - Present the form as part of AI's response */}
                    {showForm && fields.length > 0 && aiMessage && !aiIsStreaming && !aiIsTyping && (
                      <div className="flex w-full mb-[var(--spacing-12)] justify-start">
                        <div className="max-w-[95%] bg-[var(--color-background-secondary)] text-[var(--color-text-primary)] rounded-[8px] rounded-bl-[2px] shadow-sm">
                          <div className="p-[var(--spacing-16)]">
                            <p className="text-sm leading-relaxed mb-[var(--spacing-16)]">
                              Here's the form with the details I've gathered. Please review and complete any missing information:
                            </p>

                            {/* Module Selection for Projects */}
                            {entityType === 'project' && onModuleToggle && onModuleSelectionMethodChange && (
                              <div className="mb-[var(--spacing-16)]">
                                <ModuleSelector
                                  modules={DEFAULT_MODULES}
                                  selectedModules={selectedModules}
                                  recommendations={moduleRecommendations}
                                  selectionMethod={moduleSelectionMethod}
                                  showRecommendations={moduleRecommendations.length > 0}
                                  onModuleToggle={onModuleToggle}
                                  onSelectionMethodChange={onModuleSelectionMethodChange}
                                  disabled={loading}
                                />
                              </div>
                            )}

                            {/* Catalog Selection Interface */}
                            {showCatalogSelection && (
                              <div className="space-y-[var(--spacing-16)]">
                                <CatalogSelection
                                  catalogItems={catalogItems}
                                  selectedItems={selectedCatalogItems}
                                  onSelectionChange={onCatalogSelectionChange || (() => {})}
                                  loading={loading}
                                />

                                {/* Submit button for catalog selection */}
                                <div className="flex justify-end">
                                  <button
                                    onClick={onSubmit}
                                    disabled={loading || selectedCatalogItems.length === 0}
                                    className="px-[var(--spacing-16)] py-[var(--spacing-8)] bg-[var(--color-primary)] text-[var(--color-text-on-primary)] rounded-[var(--radius-4)] text-[var(--typography-body-size)] font-medium hover:bg-[var(--color-primary-hover)] disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
                                  >
                                    {loading ? (
                                      <>
                                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                        Processing...
                                      </>
                                    ) : (
                                      'Continue with Selected Items'
                                    )}
                                  </button>
                                </div>
                              </div>
                            )}

                            {/* Data Form */}
                            {!showCatalogSelection && (
                              <DataForm
                                fields={fields}
                                entityType={entityType}
                                onFieldChange={onFieldChange}
                                loading={loading}
                                submitDisabled={submitDisabled || !isFormValid}
                                onSubmit={onSubmit}
                                submitText={`Create ${entityType}`}
                              />
                            )}
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Additional info for when AI needs more details */}
                    {aiNeedsMoreInfo && (
                      <div className="text-xs text-[var(--color-text-secondary)] text-center italic">
                        💡 You can provide more details using the message bar below, or fill out the form directly.
                      </div>
                    )}
                  </div>
                )}



                {/* Refinement Instructions - Only show after AI streaming completes */}
                {fields.length > 0 && aiMessage && !aiIsStreaming && !aiIsTyping && (
                  <div className="border-t border-[var(--color-stroke)] pt-[var(--spacing-16)]">
                    <div className="space-y-[var(--spacing-8)]">
                      <h4 className="text-sm font-medium text-[var(--color-text-primary)]">
                        Refine Data
                      </h4>
                      <div className="text-xs text-[var(--color-text-secondary)]">
                        Use the main message bar below to refine this data. Try commands like "make him a senior developer" or "set salary to $80,000"
                      </div>
                    </div>
                  </div>
                )}
              </div>
              )}
            </div>
          </div>
        </ScaleIn>
      </FadeIn>
    );
  }
);

DataCreationModal.displayName = "DataCreationModal";

export { DataCreationModal };
