"use client";

import * as React from "react";
import { cn } from "./lib/utils";
import { GuidedConversation } from "./guided-conversation";
import { CatalogSelection } from "./catalog-selection";
import { ContextSelection } from "./context-selection";
import { DataCreationModalProps } from "./data-creation-modal-types";
import { DataCreationModalHeader } from "./data-creation-modal-header";
import { DataCreationModalConversation } from "./data-creation-modal-conversation";
import { DataCreationModalForms } from "./data-creation-modal-forms";
import { DataCreationModalQuickForm } from "./data-creation-modal-quick-form";

const DataCreationModal = React.forwardRef<HTMLDivElement, DataCreationModalProps>(
  ({
    className,
    isOpen,
    onClose,
    entityType,
    fields,
    loading = false,
    submitDisabled = false,
    onFieldChange,
    onSubmit,
    originalCommand,
    unknownCategoryMessage,
    aiMessage,
    aiNeedsMoreInfo = false,
    aiIsTyping = false,
    aiIsStreaming = false,
    onAiStreamingComplete,
    showForm = true,
    conversationHistory = [],
    onQuickFormSubmit,
    moduleRecommendations = [],
    selectedModules = [],
    moduleSelectionMethod = 'ai-recommended',
    onModuleToggle,
    onModuleSelectionMethodChange,
    isGuidedFlow = false,
    currentStep,
    conversationSteps = [],
    onGuidedMessage,
    showCatalogSelection = false,
    catalogItems = [],
    selectedCatalogItems = [],
    onCatalogSelectionChange,
    catalogLoading = false,
    showContextSelection = false,
    contextData,
    onContextSelectionChange,
    onContextSelectionConfirm,
  }, ref) => {
    if (!isOpen) return null;

    return (
      <div
        className={cn(
          "absolute bg-[var(--color-background-primary)] rounded-[var(--radius-8)]",
          "p-[var(--spacing-16)] flex flex-col z-50 transition-modal",
          "top-0 left-0 bottom-0 w-[400px]",
          className
        )}
        style={{
          boxShadow: 'var(--shadow-outer1)',
        }}
      >
        <div ref={ref} className="flex flex-col h-full">
          {/* Header */}
          <DataCreationModalHeader
            entityType={entityType}
            isGuidedFlow={isGuidedFlow}
            currentStep={currentStep}
            conversationSteps={conversationSteps}
            onClose={onClose}
          />

          {/* Context Selection Interface */}
          {showContextSelection && contextData && (
            <div className="mb-4">
              <ContextSelection
                contextData={contextData}
                onSelectionChange={onContextSelectionChange || (() => {})}
                onConfirmSelection={onContextSelectionConfirm || (() => {})}
                editType="team_member"
              />
            </div>
          )}

          {/* Content */}
          <div className="flex-1 overflow-y-auto flex flex-col">
            {/* Guided Conversation for Projects */}
            {isGuidedFlow && entityType === 'project' ? (
              <div className="flex-1 flex flex-col">
                {/* Guided Conversation */}
                <div className={showCatalogSelection ? "flex-shrink-0 h-80" : "flex-1"}>
                  <GuidedConversation
                    steps={conversationSteps}
                    currentStep={currentStep}
                    onSendMessage={onGuidedMessage || (() => {})}
                    messages={conversationHistory}
                    isTyping={aiIsTyping}
                    isStreaming={aiIsStreaming}
                    onStreamingComplete={onAiStreamingComplete}
                  />
                </div>

                {/* Catalog Selection Interface for Guided Flow */}
                <div className="border-t border-[var(--color-stroke)] p-[var(--spacing-16)] space-y-[var(--spacing-16)]">
                  {showCatalogSelection ? (
                    <div className="space-y-4">
                      <h3 className="text-lg font-semibold">Available Catalog Items ({catalogItems.length})</h3>
                      <CatalogSelection
                        catalogItems={catalogItems}
                        selectedItems={selectedCatalogItems}
                        onSelectionChange={onCatalogSelectionChange || (() => {})}
                        loading={catalogLoading}
                      />
                      
                      {/* Submit button for catalog selection */}
                      <div className="flex justify-end pt-4">
                        <button
                          onClick={onSubmit}
                          disabled={loading || selectedCatalogItems.length === 0}
                          className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                          {loading ? 'Creating...' : `Create ${entityType} with selected items`}
                        </button>
                      </div>
                    </div>
                  ) : null}
                </div>
              </div>
            ) : (
              /* Regular AI Conversation Flow */
              <div className="flex-1 overflow-y-auto space-y-[var(--spacing-16)]">
                {/* AI Conversation Messages */}
                <DataCreationModalConversation
                  originalCommand={originalCommand}
                  aiMessage={aiMessage}
                  conversationHistory={conversationHistory}
                  aiIsTyping={aiIsTyping}
                  aiIsStreaming={aiIsStreaming}
                  onAiStreamingComplete={onAiStreamingComplete}
                  unknownCategoryMessage={unknownCategoryMessage}
                />

                {/* Quick Form for AI Questions */}
                <DataCreationModalQuickForm
                  fields={fields}
                  aiMessage={aiMessage}
                  aiIsStreaming={aiIsStreaming}
                  aiIsTyping={aiIsTyping}
                  aiNeedsMoreInfo={aiNeedsMoreInfo}
                  showForm={showForm}
                  isGuidedFlow={isGuidedFlow}
                  conversationHistory={conversationHistory}
                  onQuickFormSubmit={onQuickFormSubmit}
                />

                {/* Main Form Response */}
                <DataCreationModalForms
                  entityType={entityType}
                  fields={fields}
                  loading={loading}
                  submitDisabled={submitDisabled}
                  onFieldChange={onFieldChange}
                  onSubmit={onSubmit}
                  aiMessage={aiMessage}
                  aiIsStreaming={aiIsStreaming}
                  aiIsTyping={aiIsTyping}
                  showForm={showForm}
                  moduleRecommendations={moduleRecommendations}
                  selectedModules={selectedModules}
                  moduleSelectionMethod={moduleSelectionMethod}
                  onModuleToggle={onModuleToggle}
                  onModuleSelectionMethodChange={onModuleSelectionMethodChange}
                  showCatalogSelection={showCatalogSelection}
                  catalogItems={catalogItems}
                  selectedCatalogItems={selectedCatalogItems}
                  onCatalogSelectionChange={onCatalogSelectionChange}
                  showContextSelection={showContextSelection}
                  contextData={contextData}
                  onContextSelectionChange={onContextSelectionChange}
                  onContextSelectionConfirm={onContextSelectionConfirm}
                />

                {/* Additional info for when AI needs more details */}
                {aiNeedsMoreInfo && (
                  <div className="text-center text-sm text-[var(--color-text-secondary)] p-[var(--spacing-12)]">
                    💡 Tip: You can provide more details or ask me to proceed with what we have
                  </div>
                )}

                {/* Refinement Instructions - Only show after AI streaming completes */}
                {fields.length > 0 && aiMessage && !aiIsStreaming && !aiIsTyping && (
                  <div className="text-center text-sm text-[var(--color-text-secondary)] p-[var(--spacing-12)]">
                    💡 You can refine any details or ask me to make changes before creating the {entityType}
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    );
  }
);

DataCreationModal.displayName = "DataCreationModal";

export { DataCreationModal };
