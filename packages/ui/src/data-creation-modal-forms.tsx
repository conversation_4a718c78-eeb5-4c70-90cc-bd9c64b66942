"use client";

import * as React from "react";
import { <PERSON>Form, FormField } from "./data-form";
import { ModuleSelector, ProjectModuleType, ModuleRecommendation, DEFAULT_MODULES } from "./module-selector";
import { CatalogSelection, SelectedCatalogItem } from "./catalog-selection";
import { ContextSelection, ContextDataResult } from "./context-selection";

interface DataCreationModalFormsProps {
  /** Entity type being created */
  entityType: string;
  /** Form fields to display */
  fields: FormField[];
  /** Whether the form is in loading state */
  loading?: boolean;
  /** Whether the form submission is disabled */
  submitDisabled?: boolean;
  /** Callback when field value changes */
  onFieldChange: (fieldId: string, value: any) => void;
  /** Callback when form is submitted */
  onSubmit: () => void;
  /** AI conversation response message */
  aiMessage?: string;
  /** Whether to stream the AI response */
  aiIsStreaming?: boolean;
  /** Whether AI is currently typing/generating response */
  aiIsTyping?: boolean;
  /** Whether to show the form or keep in conversation mode */
  showForm?: boolean;
  
  // Module Selection Props
  /** Module recommendations for project creation */
  moduleRecommendations?: ModuleRecommendation[];
  /** Selected modules for project creation */
  selectedModules?: ProjectModuleType[];
  /** Module selection method */
  moduleSelectionMethod?: 'manual' | 'ai-recommended' | 'hybrid';
  /** Callback when module selection changes */
  onModuleToggle?: (module: ProjectModuleType, enabled: boolean) => void;
  /** Callback when module selection method changes */
  onModuleSelectionMethodChange?: (method: 'manual' | 'ai-recommended' | 'hybrid') => void;
  
  // Catalog Selection Props
  /** Whether to show catalog selection interface */
  showCatalogSelection?: boolean;
  /** Available catalog items for selection */
  catalogItems?: Array<{
    id: string;
    productName: string;
    productDescription: string;
    categoryLabel: 'Product' | 'Service';
    imageSrc?: string;
  }>;
  /** Currently selected catalog items */
  selectedCatalogItems?: SelectedCatalogItem[];
  /** Callback when catalog selection changes */
  onCatalogSelectionChange?: (selectedItems: SelectedCatalogItem[]) => void;
  
  // Context Selection Props
  /** Whether to show context selection interface */
  showContextSelection?: boolean;
  /** Context data for selection */
  contextData?: ContextDataResult;
  /** Callback when context selection changes */
  onContextSelectionChange?: (itemId: string, isSelected: boolean, quantity?: number) => void;
  /** Callback when context selection is confirmed */
  onContextSelectionConfirm?: () => void;
}

export const DataCreationModalForms: React.FC<DataCreationModalFormsProps> = ({
  entityType,
  fields,
  loading,
  submitDisabled,
  onFieldChange,
  onSubmit,
  aiMessage,
  aiIsStreaming,
  aiIsTyping,
  showForm,
  moduleRecommendations,
  selectedModules,
  moduleSelectionMethod,
  onModuleToggle,
  onModuleSelectionMethodChange,
  showCatalogSelection,
  catalogItems = [],
  selectedCatalogItems = [],
  onCatalogSelectionChange,
  showContextSelection,
  contextData,
  onContextSelectionChange,
  onContextSelectionConfirm,
}) => {
  if (!showForm || fields.length === 0 || !aiMessage || aiIsStreaming || aiIsTyping) {
    return null;
  }

  return (
    <div className="bg-[var(--color-background-secondary)] rounded-[var(--radius-8)] p-[var(--spacing-16)] space-y-[var(--spacing-16)]">
      {/* Context Selection Interface */}
      {showContextSelection && contextData && (
        <div className="mb-4">
          <ContextSelection
            contextData={contextData}
            onSelectionChange={onContextSelectionChange || (() => {})}
            onConfirmSelection={onContextSelectionConfirm || (() => {})}
            editType="team_member"
          />
        </div>
      )}

      {/* Module Selection for Projects */}
      {entityType === 'project' && onModuleToggle && onModuleSelectionMethodChange && (
        <div className="space-y-[var(--spacing-12)]">
          <h3 className="text-[var(--typography-h3-size)] font-[var(--typography-h3-weight)] text-[var(--color-text-primary)]">
            Project Modules
          </h3>
          <ModuleSelector
            modules={DEFAULT_MODULES}
            recommendations={moduleRecommendations}
            selectedModules={selectedModules || []}
            selectionMethod={moduleSelectionMethod || 'ai-recommended'}
            onModuleToggle={onModuleToggle}
            onSelectionMethodChange={onModuleSelectionMethodChange}
          />
        </div>
      )}

      {/* Catalog Selection Interface */}
      {showCatalogSelection && (
        <div className="space-y-[var(--spacing-12)]">
          <CatalogSelection
            catalogItems={catalogItems}
            selectedItems={selectedCatalogItems}
            onSelectionChange={onCatalogSelectionChange || (() => {})}
            loading={loading}
          />
          
          {/* Submit button for catalog selection */}
          <div className="flex justify-end">
            <button
              onClick={onSubmit}
              disabled={loading || selectedCatalogItems.length === 0}
              className="px-[var(--spacing-16)] py-[var(--spacing-8)] bg-[var(--color-primary)] text-[var(--color-text-on-primary)] rounded-[var(--radius-4)] text-[var(--typography-body-size)] font-medium hover:bg-[var(--color-primary-hover)] disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
            >
              {loading ? (
                <div className="flex items-center space-x-2">
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  <span>Creating...</span>
                </div>
              ) : (
                `Create ${entityType} with selected items`
              )}
            </button>
          </div>
        </div>
      )}

      {/* Data Form */}
      {!showCatalogSelection && (
        <div className="space-y-[var(--spacing-12)]">
          <h3 className="text-[var(--typography-h3-size)] font-[var(--typography-h3-weight)] text-[var(--color-text-primary)]">
            Complete the details
          </h3>
          <DataForm
            fields={fields}
            entityType={entityType}
            onFieldChange={onFieldChange}
            onSubmit={onSubmit}
            loading={loading}
            submitDisabled={submitDisabled}
            submitText={`Create ${entityType}`}
          />
        </div>
      )}
    </div>
  );
};
