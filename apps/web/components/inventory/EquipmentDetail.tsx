"use client";

import React, { useState, useEffect } from 'react';
import { InventoryItem } from '../../contexts/types';
import { useInventory } from '../../contexts';
import { fetchEquipmentTypeTemplate, type EquipmentTypeTemplate } from '../../services/equipmentTemplates';
import {
  Table,
  TableHeader,
  TableHeaderRow,
  TableHeaderCell,
  TableBody,
  TableBodyRow,
  TableBodyCell,
} from '@admin/ui';

interface EquipmentDetailProps {
  item: InventoryItem;
  modalHeaderTextColor: string;
}

/**
 * Equipment detail component that shows detailed information about equipment
 * including maintenance schedules, calibration status, operating hours, and condition tracking
 */
export const EquipmentDetail: React.FC<EquipmentDetailProps> = ({
  item,
  modalHeaderTextColor
}) => {
  const { getEquipmentRecords } = useInventory();
  const [equipmentTemplate, setEquipmentTemplate] = useState<EquipmentTypeTemplate | null>(null);
  const [loading, setLoading] = useState(true);

  // Get equipment type from additional fields or fallback to default
  const equipmentType = item.additionalFields?.equipment_type || 'welding_equipment';
  const condition = item.additionalFields?.condition || 'N/A';
  
  // Get equipment records for this specific item from context
  const equipmentRecords = getEquipmentRecords(item.id);

  // Load equipment template based on equipment type
  useEffect(() => {
    const loadTemplate = async () => {
      setLoading(true);
      try {
        const template = await fetchEquipmentTypeTemplate(equipmentType);
        setEquipmentTemplate(template);
      } catch (error) {
        console.error('Failed to load equipment template:', error);
      } finally {
        setLoading(false);
      }
    };

    loadTemplate();
  }, [equipmentType]);

  if (loading || !equipmentTemplate) {
    return (
      <div className="flex items-center justify-center h-full text-[var(--color-text-secondary)]">
        Cargando detalles del equipo...
      </div>
    );
  }

  // Helper function to format cell value based on column type
  const formatCellValue = (value: any, columnType: string) => {
    // Handle null/undefined values
    if (value === null || value === undefined) {
      return 'N/A';
    }

    switch (columnType) {
      case 'currency':
        return typeof value === 'number' ? `$${value.toLocaleString()}` : 'N/A';
      case 'date':
        try {
          return new Date(value).toLocaleDateString('es-ES');
        } catch {
          return 'N/A';
        }
      case 'hours':
        return typeof value === 'number' ? `${value.toLocaleString()} hrs` : 'N/A';
      case 'status':
        // Add status styling
        const statusColors: Record<string, string> = {
          'Excelente': 'text-green-600 bg-green-50',
          'Bueno': 'text-blue-600 bg-blue-50',
          'Regular': 'text-yellow-600 bg-yellow-50',
          'Malo': 'text-red-600 bg-red-50',
          'Fuera de Servicio': 'text-gray-600 bg-gray-50',
          'Vigente': 'text-green-600 bg-green-50',
          'Vencido': 'text-red-600 bg-red-50',
          'Próximo a Vencer': 'text-yellow-600 bg-yellow-50'
        };
        const colorClass = statusColors[value] || 'text-gray-600 bg-gray-50';
        return (
          <span className={`px-2 py-1 rounded text-xs font-medium ${colorClass}`}>
            {value}
          </span>
        );
      case 'number':
        return typeof value === 'number' ? value.toString() : value?.toString() || 'N/A';
      default:
        return value?.toString() || 'N/A';
    }
  };

  // Check for maintenance alerts
  const maintenanceAlerts = equipmentRecords.filter(record => {
    if (!record.next_maintenance && !record.next_calibration && !record.next_inspection) return false;
    const today = new Date();
    const thirtyDaysFromNow = new Date();
    thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30);
    
    const nextMaintenance = record.next_maintenance ? new Date(record.next_maintenance) : null;
    const nextCalibration = record.next_calibration ? new Date(record.next_calibration) : null;
    const nextInspection = record.next_inspection ? new Date(record.next_inspection) : null;
    
    return (nextMaintenance && nextMaintenance <= thirtyDaysFromNow) ||
           (nextCalibration && nextCalibration <= thirtyDaysFromNow) ||
           (nextInspection && nextInspection <= thirtyDaysFromNow);
  });

  // Calculate average operating hours
  const totalOperatingHours = equipmentRecords.reduce((sum, record) => 
    sum + (record.operating_hours || 0), 0
  );
  const averageOperatingHours = equipmentRecords.length > 0 
    ? Math.round(totalOperatingHours / equipmentRecords.length)
    : 0;

  return (
    <div className="space-y-[var(--spacing-24)]">
      {/* Equipment Header */}
      <div className="space-y-[var(--spacing-8)]">
        <h3 className={`text-[24px] font-bold ${modalHeaderTextColor}`}>
          {item.name}
        </h3>
        <p className="modal-sub-header">
          {condition} - {equipmentTemplate.display_name}
        </p>
      </div>

      {/* Maintenance Alerts */}
      {maintenanceAlerts.length > 0 && (
        <div className="modal-section">
          <div className="p-4 bg-orange-50 border border-orange-200 rounded-[var(--radius-4)]">
            <h4 className="font-semibold text-orange-800 mb-2">🔧 Alertas de Mantenimiento</h4>
            <div className="space-y-1 text-sm text-orange-700">
              <p>• {maintenanceAlerts.length} equipo(s) requieren atención en los próximos 30 días</p>
              <p>• Revisar programación de mantenimiento, calibración o inspección</p>
            </div>
          </div>
        </div>
      )}

      {/* Equipment Records Table */}
      <div className="modal-section">
        <h4 className="modal-sub-header">
          Registros de Equipos
        </h4>
        {equipmentRecords.length > 0 ? (
        <Table>
          <TableHeader>
            <TableHeaderRow>
              {equipmentTemplate.columns.map((column, index) => (
                <TableHeaderCell 
                  key={column.id}
                  variant={
                    index === 0 ? 'first' : 
                    index === equipmentTemplate.columns.length - 1 ? 'last' : 
                    'middle'
                  }
                >
                  {column.header}
                </TableHeaderCell>
              ))}
            </TableHeaderRow>
          </TableHeader>
          <TableBody>
            {equipmentRecords.map((record, recordIndex) => {
              const totalRows = equipmentRecords.length;
              const rowPosition = recordIndex === 0 ? 'first' : recordIndex === totalRows - 1 ? 'last' : 'middle';
              return (
                <TableBodyRow key={record.id}>
                  {equipmentTemplate.columns.map((column, colIndex) => (
                    <TableBodyCell 
                      key={column.id}
                      variant={
                        colIndex === 0 ? 'first' : 
                        colIndex === equipmentTemplate.columns.length - 1 ? 'last' : 
                        'middle'
                      }
                      rowPosition={rowPosition}
                    >
                      {formatCellValue(record[column.id as keyof typeof record], column.type)}
                    </TableBodyCell>
                  ))}
                </TableBodyRow>
              );
            })}
          </TableBody>
        </Table>
        ) : (
          <div className="text-center py-8 text-[var(--color-text-secondary)]">
            No hay registros disponibles para este equipo
          </div>
        )}
      </div>

      {/* Equipment Summary */}
      <div className="modal-section">
        <h4 className="modal-sub-header">
          Resumen del Equipo
        </h4>
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <div className="flex justify-between items-center p-3 bg-[var(--color-background-secondary)] rounded-[var(--radius-4)]">
              <span className="text-sm text-[var(--color-text-secondary)]">Cantidad Total:</span>
              <span className="font-bold text-[var(--color-text-primary)]">{item.quantity} {item.unit}</span>
            </div>
            <div className="flex justify-between items-center p-3 bg-[var(--color-background-secondary)] rounded-[var(--radius-4)]">
              <span className="text-sm text-[var(--color-text-secondary)]">Estado General:</span>
              <span className={`font-bold ${
                condition === 'Excelente' ? 'text-green-600' :
                condition === 'Bueno' ? 'text-blue-600' :
                condition === 'Regular' ? 'text-yellow-600' : 'text-red-600'
              }`}>
                {condition}
              </span>
            </div>
            <div className="flex justify-between items-center p-3 bg-[var(--color-background-secondary)] rounded-[var(--radius-4)]">
              <span className="text-sm text-[var(--color-text-secondary)]">Horas Promedio:</span>
              <span className="font-bold text-[var(--color-text-primary)]">{averageOperatingHours.toLocaleString()} hrs</span>
            </div>
          </div>
          <div className="space-y-2">
            <div className="flex justify-between items-center p-3 bg-[var(--color-background-secondary)] rounded-[var(--radius-4)]">
              <span className="text-sm text-[var(--color-text-secondary)]">Ubicación:</span>
              <span className="font-bold text-[var(--color-text-primary)]">{item.location || 'N/A'}</span>
            </div>
            <div className="flex justify-between items-center p-3 bg-[var(--color-background-secondary)] rounded-[var(--radius-4)]">
              <span className="text-sm text-[var(--color-text-secondary)]">Costo Total:</span>
              <span className="font-bold text-[var(--color-text-primary)]">
                ${((item.cost || 0) * item.quantity).toLocaleString()}
              </span>
            </div>
            <div className="flex justify-between items-center p-3 bg-[var(--color-background-secondary)] rounded-[var(--radius-4)]">
              <span className="text-sm text-[var(--color-text-secondary)]">Última Actualización:</span>
              <span className="font-bold text-[var(--color-text-primary)]">{new Date(item.lastUpdated).toLocaleDateString('es-ES')}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Maintenance Schedule */}
      <div className="modal-section">
        <h4 className="modal-sub-header">
          Programación de Mantenimiento
        </h4>
        <div className="space-y-3">
          {equipmentRecords.map((record, index) => (
            <div key={record.id} className="p-4 bg-[var(--color-background-secondary)] rounded-[var(--radius-4)]">
              <div className="flex justify-between items-center mb-2">
                <span className="font-semibold text-[var(--color-text-primary)]">
                  {record.asset_tag || `Equipo ${index + 1}`}
                </span>
                <span className="text-sm text-[var(--color-text-secondary)]">
                  {record.serial_number}
                </span>
              </div>
              <div className="grid grid-cols-3 gap-4 text-sm">
                {record.next_maintenance && (
                  <div>
                    <span className="text-[var(--color-text-secondary)]">Próximo Mantenimiento:</span>
                    <p className="font-bold text-[var(--color-text-primary)]">
                      {new Date(record.next_maintenance).toLocaleDateString('es-ES')}
                    </p>
                  </div>
                )}
                {record.next_calibration && (
                  <div>
                    <span className="text-[var(--color-text-secondary)]">Próxima Calibración:</span>
                    <p className="font-bold text-[var(--color-text-primary)]">
                      {new Date(record.next_calibration).toLocaleDateString('es-ES')}
                    </p>
                  </div>
                )}
                {record.next_inspection && (
                  <div>
                    <span className="text-[var(--color-text-secondary)]">Próxima Inspección:</span>
                    <p className="font-bold text-[var(--color-text-primary)]">
                      {new Date(record.next_inspection).toLocaleDateString('es-ES')}
                    </p>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};
