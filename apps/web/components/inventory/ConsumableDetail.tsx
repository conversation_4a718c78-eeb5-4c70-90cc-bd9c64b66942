"use client";

import React, { useState, useEffect } from 'react';
import { InventoryItem } from '../../contexts/types';
import { useInventory } from '../../contexts';
import { fetchConsumableTypeTemplate, type ConsumableTypeTemplate } from '../../services/consumableTemplates';
import {
  Table,
  TableHeader,
  TableHeaderRow,
  TableHeaderCell,
  TableBody,
  TableBodyRow,
  TableBodyCell,
} from '@admin/ui';

interface ConsumableDetailProps {
  item: InventoryItem;
  modalHeaderTextColor: string;
}

/**
 * Consumable detail component that shows detailed information about consumables
 * including batch information, usage tracking, expiry dates, and supplier details with dynamic table headers
 */
export const ConsumableDetail: React.FC<ConsumableDetailProps> = ({
  item,
  modalHeaderTextColor
}) => {
  const { getConsumableBatches } = useInventory();
  const [consumableTemplate, setConsumableTemplate] = useState<ConsumableTypeTemplate | null>(null);
  const [loading, setLoading] = useState(true);

  // Get consumable type from additional fields or fallback to default
  const consumableType = item.additionalFields?.consumable_type || 'welding_supplies';
  const usageRate = item.additionalFields?.usage_rate || 'N/A';
  
  // Get batch data for this specific item from context
  const batches = getConsumableBatches(item.id);

  // Load consumable template based on consumable type
  useEffect(() => {
    const loadTemplate = async () => {
      setLoading(true);
      try {
        const template = await fetchConsumableTypeTemplate(consumableType);
        setConsumableTemplate(template);
      } catch (error) {
        console.error('Failed to load consumable template:', error);
      } finally {
        setLoading(false);
      }
    };

    loadTemplate();
  }, [consumableType]);

  if (loading || !consumableTemplate) {
    return (
      <div className="flex items-center justify-center h-full text-[var(--color-text-secondary)]">
        Cargando detalles del consumible...
      </div>
    );
  }

  // Helper function to format cell value based on column type
  const formatCellValue = (value: any, columnType: string) => {
    // Handle null/undefined values
    if (value === null || value === undefined) {
      return 'N/A';
    }

    switch (columnType) {
      case 'currency':
        return typeof value === 'number' ? `$${value.toLocaleString()}` : 'N/A';
      case 'date':
        try {
          return new Date(value).toLocaleDateString('es-ES');
        } catch {
          return 'N/A';
        }
      case 'percentage':
        return typeof value === 'number' ? `${value}%` : 'N/A';
      case 'number':
        return typeof value === 'number' ? value.toString() : value?.toString() || 'N/A';
      default:
        return value?.toString() || 'N/A';
    }
  };

  // Calculate total usage percentage across all batches
  const totalUsagePercentage = batches.length > 0 
    ? Math.round(batches.reduce((sum, batch) => sum + (batch.usage_percentage || 0), 0) / batches.length)
    : 0;

  // Check if any batches are near expiry (within 30 days)
  const nearExpiryBatches = batches.filter(batch => {
    if (!batch.expiry_date) return false;
    const expiryDate = new Date(batch.expiry_date);
    const thirtyDaysFromNow = new Date();
    thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30);
    return expiryDate <= thirtyDaysFromNow;
  });

  return (
    <div className="space-y-[var(--spacing-24)]">
      {/* Consumable Header */}
      <div className="space-y-[var(--spacing-8)]">
        <h3 className={`text-[24px] font-bold ${modalHeaderTextColor}`}>
          {item.name}
        </h3>
        <p className="modal-sub-header">
          {usageRate} usado - {consumableTemplate.display_name}
        </p>
      </div>

      {/* Usage Alert */}
      {(totalUsagePercentage > 75 || nearExpiryBatches.length > 0) && (
        <div className="modal-section">
          <div className="p-4 bg-orange-50 border border-orange-200 rounded-[var(--radius-4)]">
            <h4 className="font-semibold text-orange-800 mb-2">⚠️ Alertas de Inventario</h4>
            <div className="space-y-1 text-sm text-orange-700">
              {totalUsagePercentage > 75 && (
                <p>• Stock bajo: {totalUsagePercentage}% del inventario ha sido utilizado</p>
              )}
              {nearExpiryBatches.length > 0 && (
                <p>• {nearExpiryBatches.length} lote(s) próximo(s) a vencer en los próximos 30 días</p>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Consumable Batches Table */}
      <div className="modal-section">
        <h4 className="modal-sub-header">
          Lotes de Consumibles
        </h4>
        {batches.length > 0 ? (
        <Table>
          <TableHeader>
            <TableHeaderRow>
              {consumableTemplate.columns.map((column, index) => (
                <TableHeaderCell 
                  key={column.id}
                  variant={
                    index === 0 ? 'first' : 
                    index === consumableTemplate.columns.length - 1 ? 'last' : 
                    'middle'
                  }
                >
                  {column.header}
                </TableHeaderCell>
              ))}
            </TableHeaderRow>
          </TableHeader>
          <TableBody>
            {batches.map((batch, batchIndex) => {
              const totalRows = batches.length;
              const rowPosition = batchIndex === 0 ? 'first' : batchIndex === totalRows - 1 ? 'last' : 'middle';
              return (
                <TableBodyRow key={batch.id}>
                  {consumableTemplate.columns.map((column, colIndex) => (
                    <TableBodyCell 
                      key={column.id}
                      variant={
                        colIndex === 0 ? 'first' : 
                        colIndex === consumableTemplate.columns.length - 1 ? 'last' : 
                        'middle'
                      }
                      rowPosition={rowPosition}
                    >
                      {formatCellValue(batch[column.id as keyof typeof batch], column.type)}
                    </TableBodyCell>
                  ))}
                </TableBodyRow>
              );
            })}
          </TableBody>
        </Table>
        ) : (
          <div className="text-center py-8 text-[var(--color-text-secondary)]">
            No hay lotes disponibles para este consumible
          </div>
        )}
      </div>

      {/* Consumable Summary */}
      <div className="modal-section">
        <h4 className="modal-sub-header">
          Resumen del Consumible
        </h4>
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <div className="flex justify-between items-center p-3 bg-[var(--color-background-secondary)] rounded-[var(--radius-4)]">
              <span className="text-sm text-[var(--color-text-secondary)]">Stock Actual:</span>
              <span className="font-bold text-[var(--color-text-primary)]">{item.quantity} {item.unit}</span>
            </div>
            <div className="flex justify-between items-center p-3 bg-[var(--color-background-secondary)] rounded-[var(--radius-4)]">
              <span className="text-sm text-[var(--color-text-secondary)]">Stock Mínimo:</span>
              <span className={`font-bold ${item.quantity <= (item.minStock || 0) ? 'text-red-600' : 'text-orange-600'}`}>
                {item.minStock || 'N/A'} {item.unit}
              </span>
            </div>
            <div className="flex justify-between items-center p-3 bg-[var(--color-background-secondary)] rounded-[var(--radius-4)]">
              <span className="text-sm text-[var(--color-text-secondary)]">Uso Promedio:</span>
              <span className="font-bold text-[var(--color-text-primary)]">{totalUsagePercentage}%</span>
            </div>
          </div>
          <div className="space-y-2">
            <div className="flex justify-between items-center p-3 bg-[var(--color-background-secondary)] rounded-[var(--radius-4)]">
              <span className="text-sm text-[var(--color-text-secondary)]">Ubicación:</span>
              <span className="font-bold text-[var(--color-text-primary)]">{item.location || 'N/A'}</span>
            </div>
            <div className="flex justify-between items-center p-3 bg-[var(--color-background-secondary)] rounded-[var(--radius-4)]">
              <span className="text-sm text-[var(--color-text-secondary)]">Proveedor:</span>
              <span className="font-bold text-[var(--color-text-primary)]">{item.supplier || 'N/A'}</span>
            </div>
            <div className="flex justify-between items-center p-3 bg-[var(--color-background-secondary)] rounded-[var(--radius-4)]">
              <span className="text-sm text-[var(--color-text-secondary)]">Última Actualización:</span>
              <span className="font-bold text-[var(--color-text-primary)]">{new Date(item.lastUpdated).toLocaleDateString('es-ES')}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Reorder Information */}
      <div className="modal-section">
        <h4 className="modal-sub-header">
          Información de Reorden
        </h4>
        <div className="p-4 bg-[var(--color-background-secondary)] rounded-[var(--radius-4)]">
          <div className="grid grid-cols-3 gap-4">
            <div>
              <span className="text-sm text-[var(--color-text-secondary)]">Punto de Reorden:</span>
              <p className="font-bold text-[var(--color-text-primary)]">
                {item.additionalFields?.reorder_point || 'No definido'}
              </p>
            </div>
            <div>
              <span className="text-sm text-[var(--color-text-secondary)]">Stock de Seguridad:</span>
              <p className="font-bold text-[var(--color-text-primary)]">
                {item.additionalFields?.safety_stock || 'No definido'}
              </p>
            </div>
            <div>
              <span className="text-sm text-[var(--color-text-secondary)]">Estado de Reorden:</span>
              <p className={`font-bold ${
                item.quantity <= (item.additionalFields?.reorder_point || 0) 
                  ? 'text-red-600' 
                  : 'text-green-600'
              }`}>
                {item.quantity <= (item.additionalFields?.reorder_point || 0) 
                  ? 'Reorden Necesario' 
                  : 'Stock Suficiente'
                }
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
