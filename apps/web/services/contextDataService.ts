import { TeamMember, CatalogItem, InventoryItem, ProjectTask } from '../contexts/types';

/**
 * Interface for selectable items in project editing
 */
export interface SelectableItem {
  id: string;
  name: string;
  description?: string;
  type: 'team_member' | 'catalog_item' | 'inventory_item' | 'task_template';
  category?: string;
  metadata?: Record<string, any>;
  isSelected?: boolean;
  quantity?: number;
}

/**
 * Interface for context data with selection capabilities
 */
export interface ContextDataResult {
  items: SelectableItem[];
  totalCount: number;
  categories?: string[];
  hasMore?: boolean;
}

/**
 * Service for fetching and formatting context data for project editing
 */
export class ContextDataService {
  
  /**
   * Fetch team members from TeamContext
   */
  static async fetchTeamMembers(): Promise<ContextDataResult> {
    try {
      // Import contexts dynamically to avoid circular dependencies
      const { mockTeamMembers } = await import('../contexts/mockData');
      
      const items: SelectableItem[] = mockTeamMembers.map((member: TeamMember) => ({
        id: member.id,
        name: member.name,
        description: `${member.role} - ${member.email}`,
        type: 'team_member' as const,
        category: member.role,
        metadata: {
          role: member.role,
          email: member.email,
          status: member.status,
          hourlyRate: member.hourlyRate,
          skills: member.skills || [],
          avatar: member.avatar
        },
        isSelected: false
      }));

      return {
        items,
        totalCount: items.length,
        categories: [...new Set(mockTeamMembers.map(m => m.role))]
      };
    } catch (error) {
      console.error('Error fetching team members:', error);
      return { items: [], totalCount: 0 };
    }
  }

  /**
   * Fetch catalog items from CatalogContext
   */
  static async fetchCatalogItems(filter?: { type?: 'Product' | 'Service' }): Promise<ContextDataResult> {
    try {
      const { mockCatalogItems } = await import('../contexts/mockData');
      
      let filteredItems = mockCatalogItems;
      if (filter?.type) {
        filteredItems = mockCatalogItems.filter(item => item.categoryLabel === filter.type);
      }

      const items: SelectableItem[] = filteredItems.map((item: CatalogItem) => ({
        id: item.id,
        name: item.productName,
        description: item.productDescription,
        type: 'catalog_item' as const,
        category: item.categoryLabel,
        metadata: {
          categoryLabel: item.categoryLabel,
          imageSrc: item.imageSrc,
          details: item.details
        },
        isSelected: false,
        quantity: 1
      }));

      return {
        items,
        totalCount: items.length,
        categories: ['Product', 'Service']
      };
    } catch (error) {
      console.error('Error fetching catalog items:', error);
      return { items: [], totalCount: 0 };
    }
  }

  /**
   * Fetch inventory items from InventoryContext
   */
  static async fetchInventoryItems(filter?: { category?: string }): Promise<ContextDataResult> {
    try {
      const { mockInventoryItems } = await import('../contexts/mockData');
      
      let filteredItems = mockInventoryItems;
      if (filter?.category) {
        filteredItems = mockInventoryItems.filter(item => item.category === filter.category);
      }

      const items: SelectableItem[] = filteredItems.map((item: InventoryItem) => ({
        id: item.id,
        name: item.name,
        description: `${item.description} - ${item.quantity} ${item.unit} available`,
        type: 'inventory_item' as const,
        category: item.category,
        metadata: {
          currentQuantity: item.quantity,
          unit: item.unit,
          cost: item.cost,
          location: item.location,
          supplier: item.supplier,
          categoryId: item.categoryId,
          additionalFields: item.additionalFields
        },
        isSelected: false,
        quantity: 1
      }));

      return {
        items,
        totalCount: items.length,
        categories: [...new Set(mockInventoryItems.map(item => item.category))]
      };
    } catch (error) {
      console.error('Error fetching inventory items:', error);
      return { items: [], totalCount: 0 };
    }
  }

  /**
   * Fetch task templates (for now, return common task templates)
   */
  static async fetchTaskTemplates(): Promise<ContextDataResult> {
    try {
      // Common task templates for fabrication business
      const taskTemplates = [
        {
          id: 'task-template-1',
          name: 'Design Review',
          description: 'Review and approve design specifications',
          category: 'Design',
          estimatedDuration: '2-3 days'
        },
        {
          id: 'task-template-2',
          name: 'Material Procurement',
          description: 'Source and purchase required materials',
          category: 'Procurement',
          estimatedDuration: '1-2 weeks'
        },
        {
          id: 'task-template-3',
          name: 'Fabrication',
          description: 'Manufacturing and assembly process',
          category: 'Production',
          estimatedDuration: '1-4 weeks'
        },
        {
          id: 'task-template-4',
          name: 'Quality Control',
          description: 'Inspect and test finished products',
          category: 'Quality',
          estimatedDuration: '1-2 days'
        },
        {
          id: 'task-template-5',
          name: 'Installation',
          description: 'On-site installation and setup',
          category: 'Installation',
          estimatedDuration: '3-5 days'
        },
        {
          id: 'task-template-6',
          name: 'Project Handover',
          description: 'Final delivery and documentation',
          category: 'Completion',
          estimatedDuration: '1 day'
        }
      ];

      const items: SelectableItem[] = taskTemplates.map(template => ({
        id: template.id,
        name: template.name,
        description: template.description,
        type: 'task_template' as const,
        category: template.category,
        metadata: {
          estimatedDuration: template.estimatedDuration,
          isTemplate: true
        },
        isSelected: false
      }));

      return {
        items,
        totalCount: items.length,
        categories: [...new Set(taskTemplates.map(t => t.category))]
      };
    } catch (error) {
      console.error('Error fetching task templates:', error);
      return { items: [], totalCount: 0 };
    }
  }

  /**
   * Get context data based on edit type
   */
  static async getContextDataForEditType(editType: string, filter?: any): Promise<ContextDataResult> {
    switch (editType) {
      case 'add_team_member':
        return await this.fetchTeamMembers();
      
      case 'add_product':
        return await this.fetchCatalogItems({ type: 'Product' });
      
      case 'add_service':
        return await this.fetchCatalogItems({ type: 'Service' });
      
      case 'add_material':
        return await this.fetchInventoryItems(filter);
      
      case 'add_task':
        return await this.fetchTaskTemplates();
      
      default:
        return { items: [], totalCount: 0 };
    }
  }

  /**
   * Update selection state for items
   */
  static updateItemSelection(items: SelectableItem[], itemId: string, isSelected: boolean, quantity?: number): SelectableItem[] {
    return items.map(item => {
      if (item.id === itemId) {
        return {
          ...item,
          isSelected,
          quantity: quantity !== undefined ? quantity : item.quantity
        };
      }
      return item;
    });
  }

  /**
   * Get selected items from a list
   */
  static getSelectedItems(items: SelectableItem[]): SelectableItem[] {
    return items.filter(item => item.isSelected);
  }

  /**
   * Format selected items for project integration
   */
  static formatSelectedItemsForProject(selectedItems: SelectableItem[], editType: string) {
    return selectedItems.map(item => {
      const baseData = {
        id: item.id,
        name: item.name,
        description: item.description,
        quantity: item.quantity || 1,
        metadata: item.metadata
      };

      switch (editType) {
        case 'add_team_member':
          return {
            ...baseData,
            role: item.metadata?.role,
            email: item.metadata?.email,
            hourlyRate: item.metadata?.hourlyRate,
            skills: item.metadata?.skills || []
          };
        
        case 'add_product':
        case 'add_service':
          return {
            ...baseData,
            categoryLabel: item.metadata?.categoryLabel,
            imageSrc: item.metadata?.imageSrc,
            details: item.metadata?.details
          };
        
        case 'add_material':
          return {
            ...baseData,
            unit: item.metadata?.unit,
            cost: item.metadata?.cost,
            currentQuantity: item.metadata?.currentQuantity,
            location: item.metadata?.location,
            supplier: item.metadata?.supplier
          };
        
        case 'add_task':
          return {
            ...baseData,
            category: item.category,
            estimatedDuration: item.metadata?.estimatedDuration,
            isFromTemplate: item.metadata?.isTemplate
          };
        
        default:
          return baseData;
      }
    });
  }
}

export default ContextDataService;
