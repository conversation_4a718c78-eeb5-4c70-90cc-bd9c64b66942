import OpenAI from 'openai';

// Lazy initialization of OpenAI client
let openai: OpenAI | null = null;

export function getOpenAIClient(): OpenAI {
  if (!openai) {
    openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY || '********************************************************************************************************************************************************************',
      dangerouslyAllowBrowser: true // For client-side usage in development
    });
  }
  return openai;
}

// Supported entity types for data creation
export type EntityType = 'employee' | 'project' | 'inventory' | 'financial' | 'catalog';

// Intent extraction result
export interface ParsedIntent {
  action: 'create' | 'update' | 'delete' | 'remove' | 'edit' | 'modify' | 'add_to' | 'unknown';
  entityType: EntityType | null;
  extractedData: Record<string, any>;
  confidence: number;
  originalCommand: string;
  // For edit actions, specify the target project and what to modify
  targetProject?: string; // Project name or ID
  editType?: 'add_team_member' | 'add_task' | 'add_product' | 'add_service' | 'add_material' | 'enable_module' | 'update_info';
}

// Entity field definitions for form generation
export interface EntityField {
  id: string;
  label: string;
  type: 'text' | 'number' | 'email' | 'select' | 'date' | 'currency';
  required: boolean;
  options?: string[];
  placeholder?: string;
  defaultValue?: any;
}

// Entity schema definitions
export const ENTITY_SCHEMAS: Record<EntityType, EntityField[]> = {
  employee: [
    { id: 'name', label: 'Full Name', type: 'text', required: true, placeholder: 'Enter full name' },
    { id: 'role', label: 'Job Role', type: 'text', required: true, placeholder: 'e.g., Senior Developer' },
    { id: 'email', label: 'Email', type: 'email', required: true, placeholder: '<EMAIL>' },
    { id: 'salary', label: 'Salary', type: 'currency', required: true, placeholder: '50000' },
    { id: 'currency', label: 'Currency', type: 'select', required: true, options: ['USD', 'EUR', 'MXN'], defaultValue: 'USD' },
    { id: 'status', label: 'Status', type: 'select', required: true, options: ['active', 'inactive'], defaultValue: 'active' }
  ],
  project: [
    // Basic project information only - modules will be added via edit operations
    { id: 'name', label: 'Project Name', type: 'text', required: true, placeholder: 'Enter project name' },
    { id: 'client', label: 'Client Name', type: 'text', required: true, placeholder: 'Enter client name' },
    { id: 'description', label: 'Description', type: 'text', required: false, placeholder: 'Project description' },
    { id: 'startDate', label: 'Start Date', type: 'date', required: true },
    { id: 'endDate', label: 'End Date', type: 'date', required: false }
  ],
  inventory: [
    { id: 'name', label: 'Item Name', type: 'text', required: true, placeholder: 'Enter item name' },
    { id: 'description', label: 'Description', type: 'text', required: true, placeholder: 'Item description' },
    { id: 'quantity', label: 'Quantity', type: 'number', required: true, placeholder: '0' },
    { id: 'unit', label: 'Unit', type: 'select', required: true, options: ['piezas', 'kg', 'litros', 'metros'], defaultValue: 'piezas' },
    { id: 'category', label: 'Category', type: 'select', required: true, options: ['Materia Prima', 'Herramientas y Equipos', 'Consumibles', 'Productos Terminados'], defaultValue: 'Materia Prima' },
    { id: 'cost', label: 'Cost per unit', type: 'currency', required: true, placeholder: '0.00', defaultValue: '0' },
    { id: 'location', label: 'Location', type: 'text', required: false, placeholder: 'Storage location' },
    { id: 'supplier', label: 'Supplier', type: 'text', required: false, placeholder: 'Supplier name' },
    // Payment method fields for purchase tracking (always shown in Financial Registry section)
    { id: 'paymentMethod', label: 'Payment Method', type: 'select', required: false, options: ['cash', 'credit_card', 'loan', 'bank_transfer', 'check'], defaultValue: 'cash' },
    { id: 'isRecurring', label: 'Recurring Payment', type: 'select', required: false, options: ['yes', 'no'], defaultValue: 'no' },
    { id: 'monthlyAmount', label: 'Monthly Payment', type: 'currency', required: false, placeholder: '0' },
    { id: 'totalMonths', label: 'Total Months', type: 'number', required: false, placeholder: '12' },
    { id: 'interestRate', label: 'Interest Rate (%)', type: 'number', required: false, placeholder: '5.5' },
    { id: 'dueDate', label: 'Due Date', type: 'date', required: false }
  ],
  financial: [
    { id: 'type', label: 'Type', type: 'select', required: true, options: ['income', 'expense'], defaultValue: 'expense' },
    { id: 'amount', label: 'Amount', type: 'currency', required: true, placeholder: '0' },
    { id: 'description', label: 'Description', type: 'text', required: true, placeholder: 'Transaction description' },
    { id: 'date', label: 'Date', type: 'date', required: true },
    { id: 'category', label: 'Category', type: 'text', required: true, placeholder: 'e.g., Materials, Sales' },
    { id: 'paymentMethod', label: 'Payment Method', type: 'select', required: true, options: ['cash', 'credit_card', 'loan', 'bank_transfer', 'check'], defaultValue: 'cash' },
    { id: 'isRecurring', label: 'Recurring Payment', type: 'select', required: false, options: ['yes', 'no'], defaultValue: 'no' },
    { id: 'monthlyAmount', label: 'Monthly Payment', type: 'currency', required: false, placeholder: '0' },
    { id: 'totalMonths', label: 'Total Months', type: 'number', required: false, placeholder: '12' },
    { id: 'interestRate', label: 'Interest Rate (%)', type: 'number', required: false, placeholder: '5.5' },
    { id: 'dueDate', label: 'Due Date', type: 'date', required: false }
  ],
  catalog: [
    { id: 'productName', label: 'Product Name', type: 'text', required: true, placeholder: 'Enter product name' },
    { id: 'productDescription', label: 'Description', type: 'text', required: true, placeholder: 'Product description' },
    { id: 'categoryLabel', label: 'Category', type: 'select', required: true, options: ['Product', 'Service'], defaultValue: 'Product' }
  ]
};

// AI conversation response types
export interface AIConversationResponse {
  type: 'question' | 'confirmation' | 'data_ready' | 'error' | 'catalog_selection';
  message: string;
  data?: Record<string, any>;
  followUpQuestions?: string[];
  needsMoreInfo?: boolean;
  extractedData?: Record<string, any>;
  businessTemplate?: any;
  showModuleSelection?: boolean;
}

/**
 * Generate a conversational AI response for data creation
 */
export async function generateConversationalResponse(
  command: string,
  intent: ParsedIntent,
  entityType: EntityType,
  extractedData: Record<string, any>
): Promise<AIConversationResponse> {
  try {
    const systemPrompt = `You are a friendly AI assistant helping users create business data. Your primary approach is to ask relevant questions BEFORE showing forms to ensure you gather complete, accurate information.

PRIORITY: Always ask questions first unless the user has provided comprehensive details.

Entity type: ${entityType}
User command: "${command}"
Extracted data: ${JSON.stringify(extractedData)}

Entity-specific question guidelines:
- EMPLOYEE: Ask about role/position, department first, then salary, start date
- PROJECT: Ask about project name and client name first, then project type, timeline, budget
- INVENTORY: Ask about specific item type and quantity first, then supplier, cost
- FINANCIAL: Ask about transaction type, amount, payment method, date, category
- CATALOG: Ask about product/service details, pricing, categories, descriptions

Response strategy:
1. FIRST: Analyze what's missing for a complete ${entityType} record
2. THEN: Ask 1-2 specific, relevant questions to gather key missing information
3. ONLY use "data_ready" when you have sufficient details for a meaningful record

Response types:
- "question": DEFAULT - Ask for missing critical information (use this most often)
- "confirmation": When you have most info but want to confirm understanding
- "data_ready": ONLY when you have enough details for a complete, useful record

IMPORTANT: Return ONLY valid JSON. No explanatory text, markdown, or additional content.

JSON structure:
{
  "type": "question" | "confirmation" | "data_ready",
  "message": "Your conversational response to the user",
  "needsMoreInfo": true/false,
  "followUpQuestions": ["question1", "question2"]
}

Examples:
- For "add employee John Smith": {"type": "question", "message": "I'll help you add John Smith as a new employee! To set up his profile properly, what role will he be taking on? And which department will he be joining?", "needsMoreInfo": true, "followUpQuestions": ["What role/position?", "Which department?"]}
- For "create a new project": {"type": "question", "message": "Great! To start creating your new project, could you please provide the name of the project? And who is the client for this project?", "needsMoreInfo": true, "followUpQuestions": ["What's the project name?", "Who is the client?"]}
- For "buy materials": {"type": "question", "message": "I'd be happy to help you add materials to inventory! What specific materials are you looking to purchase? And how many do you need?", "needsMoreInfo": true, "followUpQuestions": ["What type of materials?", "How many do you need?"]}
- For "create project for ABC Corp with $50k budget and 3 month timeline": {"type": "confirmation", "message": "Perfect! I'll create a project for ABC Corp with a $50,000 budget and 3-month timeline. What should we call this project?", "needsMoreInfo": true, "followUpQuestions": ["What's the project name?"]}`;

    const response = await getOpenAIClient().chat.completions.create({
      model: 'gpt-3.5-turbo',
      messages: [
        { role: 'system', content: systemPrompt },
        { role: 'user', content: `Command: "${command}"\nExtracted: ${JSON.stringify(extractedData)}` }
      ],
      temperature: 0.3,
      max_tokens: 300
    });

    const content = response.choices[0]?.message?.content;
    if (!content) {
      throw new Error('No response from OpenAI');
    }

    // Parse the JSON response
    let cleanContent = content.trim();
    const firstBrace = cleanContent.indexOf('{');
    const lastBrace = cleanContent.lastIndexOf('}');

    if (firstBrace !== -1 && lastBrace !== -1 && lastBrace > firstBrace) {
      cleanContent = cleanContent.substring(firstBrace, lastBrace + 1);
    }

    try {
      const parsed = JSON.parse(cleanContent) as AIConversationResponse;
      return parsed;
    } catch (jsonError) {
      console.warn('Failed to parse AI conversation response:', cleanContent);
      console.warn('JSON parse error:', jsonError);
      // Return fallback response that asks questions instead of showing form
      return {
        type: 'question',
        message: `I'll help you create a new ${entityType}. Could you provide more details about what you'd like to create?`,
        needsMoreInfo: true,
        followUpQuestions: [`What specific details should I know about this ${entityType}?`]
      };
    }

  } catch (error) {
    console.error('Error generating conversational response:', error);
    return {
      type: 'question',
      message: `I'll help you create a new ${entityType}. Could you tell me more about what you'd like to create?`,
      needsMoreInfo: true,
      followUpQuestions: [`What details should I know about this ${entityType}?`]
    };
  }
}

/**
 * Parse natural language command to extract intent and data
 */
export async function parseCommand(command: string): Promise<ParsedIntent> {
  try {
    const systemPrompt = `You are an AI assistant that parses natural language commands for a business management system.
    
Your task is to:
1. Identify the action (create, update, delete, remove, edit, modify, add_to)
2. Identify the entity type (employee, project, inventory, financial, catalog)
3. Extract any data mentioned in the command (for delete/remove, extract identifying information like name, id, etc.)
4. For edit/modify/add_to actions, identify the target project and what to modify
5. Return a confidence score (0-1)

Supported entity types:
- employee: team members, staff, workers, employees
- project: projects, jobs, contracts, work orders
- inventory: items, materials, products, stock, equipment, purchases, buying items
- financial: transactions, expenses, income, payments, costs, credit card purchases, loans, monthly payments
- catalog: catalog items, services, offerings

For inventory purchases, pay special attention to:
- Payment method: cash, credit_card, loan, bank_transfer, check
- Recurring payments: if user mentions "monthly", "loan", "credit card", "installments", set isRecurring to "yes"
- For loans/credit cards: extract monthlyAmount, totalMonths, interestRate if mentioned
- Due dates: for loans or credit card payments

For financial transactions, pay special attention to:
- Payment method: cash, credit_card, loan, bank_transfer, check
- Recurring payments: if user mentions "monthly", "loan", "credit card", "installments", set isRecurring to "yes"
- For loans/credit cards: extract monthlyAmount, totalMonths, interestRate if mentioned
- Due dates: for loans or credit card payments

IMPORTANT: Return ONLY a valid JSON object. Do not include any explanatory text, markdown formatting, or additional content. Just the raw JSON.

JSON structure:
{
  "action": "create" | "update" | "delete" | "remove" | "edit" | "modify" | "add_to" | "unknown",
  "entityType": "employee" | "project" | "inventory" | "financial" | "catalog" | null,
  "extractedData": {},
  "confidence": 0.95,
  "originalCommand": "the original command",
  "targetProject": "project name or id (for edit actions)",
  "editType": "add_team_member" | "add_task" | "add_product" | "add_service" | "add_material" | "enable_module" | "update_info"
}

Examples:
- "add a new employee named Mark" → {"action": "create", "entityType": "employee", "extractedData": {"name": "Mark"}, "confidence": 0.95}
- "create project for Tommy Hilfiger store" → {"action": "create", "entityType": "project", "extractedData": {"name": "Tommy Hilfiger store"}, "confidence": 0.9}
- "add steel sheets to inventory" → {"action": "create", "entityType": "inventory", "extractedData": {"name": "steel sheets"}, "confidence": 0.85}
- "delete employee Mark" → {"action": "delete", "entityType": "employee", "extractedData": {"name": "Mark"}, "confidence": 0.9}
- "remove steel sheets from inventory" → {"action": "remove", "entityType": "inventory", "extractedData": {"name": "steel sheets"}, "confidence": 0.85}
- "delete project Tommy Hilfiger" → {"action": "delete", "entityType": "project", "extractedData": {"name": "Tommy Hilfiger"}, "confidence": 0.9}
- "remove catalog item office chair" → {"action": "delete", "entityType": "catalog", "extractedData": {"name": "office chair"}, "confidence": 0.9}
- "delete financial record from last month" → {"action": "delete", "entityType": "financial", "extractedData": {"timeframe": "last month"}, "confidence": 0.8}
- "eliminate project Cuernavaca" → {"action": "delete", "entityType": "project", "extractedData": {"name": "Cuernavaca"}, "confidence": 0.9}
- "delete project" → {"action": "delete", "entityType": "project", "extractedData": {}, "confidence": 0.8}
- "remove employee" → {"action": "delete", "entityType": "employee", "extractedData": {}, "confidence": 0.8}
- "delete inventory item" → {"action": "delete", "entityType": "inventory", "extractedData": {}, "confidence": 0.8}
- "edit Tommy Hilfiger project to add John as team member" → {"action": "edit", "entityType": "project", "extractedData": {"name": "John"}, "targetProject": "Tommy Hilfiger", "editType": "add_team_member", "confidence": 0.9}
- "add steel sheets to Cuernavaca project" → {"action": "add_to", "entityType": "project", "extractedData": {"name": "steel sheets"}, "targetProject": "Cuernavaca", "editType": "add_material", "confidence": 0.85}
- "modify project ABC to include installation service" → {"action": "modify", "entityType": "project", "extractedData": {"name": "installation service"}, "targetProject": "ABC", "editType": "add_service", "confidence": 0.9}
- "update Tommy Hilfiger project with new task" → {"action": "update", "entityType": "project", "extractedData": {"name": "new task"}, "targetProject": "Tommy Hilfiger", "editType": "add_task", "confidence": 0.85}`;

    const response = await getOpenAIClient().chat.completions.create({
      model: 'gpt-3.5-turbo',
      messages: [
        { role: 'system', content: systemPrompt },
        { role: 'user', content: command }
      ],
      temperature: 0.1,
      max_tokens: 500
    });

    const content = response.choices[0]?.message?.content;
    if (!content) {
      throw new Error('No response from OpenAI');
    }

    // Clean and parse the JSON response
    let cleanContent = content.trim();

    // Remove any text before the first { and after the last }
    const firstBrace = cleanContent.indexOf('{');
    const lastBrace = cleanContent.lastIndexOf('}');

    if (firstBrace !== -1 && lastBrace !== -1 && lastBrace > firstBrace) {
      cleanContent = cleanContent.substring(firstBrace, lastBrace + 1);
    }

    let parsed: ParsedIntent;
    try {
      parsed = JSON.parse(cleanContent) as ParsedIntent;
    } catch (jsonError) {
      console.warn('Failed to parse OpenAI JSON response:', cleanContent);
      // Return fallback response for JSON parse errors
      return {
        action: 'unknown',
        entityType: null,
        extractedData: {},
        confidence: 0,
        originalCommand: command
      };
    }
    
    // Validate the response structure with more lenient checks
    if (!parsed.action || !parsed.hasOwnProperty('entityType') || !parsed.extractedData || typeof parsed.confidence !== 'number') {
      console.warn('Invalid response structure from OpenAI, using fallback:', parsed);
      // Return a fallback response instead of throwing
      return {
        action: 'unknown',
        entityType: null,
        extractedData: {},
        confidence: 0,
        originalCommand: command
      };
    }

    return {
      ...parsed,
      originalCommand: command
    };

  } catch (error) {
    console.error('Error parsing command:', error);

    // Return a fallback response
    return {
      action: 'unknown',
      entityType: null,
      extractedData: {},
      confidence: 0,
      originalCommand: command
    };
  }
}

/**
 * Refine existing data based on follow-up command
 */
export async function refineData(
  originalData: Record<string, any>,
  entityType: EntityType,
  refinementCommand: string
): Promise<Record<string, any>> {
  try {
    const schema = ENTITY_SCHEMAS[entityType];
    const fieldDescriptions = schema.map(field =>
      `${field.id}: ${field.label} (${field.type}${field.options ? `, options: ${field.options.join(', ')}` : ''})`
    ).join('\n');

    const systemPrompt = `You are an AI assistant that refines data based on user commands.

Current data: ${JSON.stringify(originalData, null, 2)}

Entity type: ${entityType}
Available fields:
${fieldDescriptions}

The user wants to modify this data. Parse their command and return the updated data object.

IMPORTANT: Return ONLY a valid JSON object with the modified fields. Do not include any explanatory text, markdown formatting, or additional content. Just the raw JSON. Keep existing fields unchanged unless specifically modified.

Examples:
- "make him a senior developer" → update role field
- "set salary to 80000" → update salary field
- "change status to inactive" → update status field`;

    const response = await getOpenAIClient().chat.completions.create({
      model: 'gpt-3.5-turbo',
      messages: [
        { role: 'system', content: systemPrompt },
        { role: 'user', content: refinementCommand }
      ],
      temperature: 0.1,
      max_tokens: 500
    });

    const content = response.choices[0]?.message?.content;
    if (!content) {
      throw new Error('No response from OpenAI');
    }

    // Clean and parse the JSON response
    let cleanContent = content.trim();

    // Remove any text before the first { and after the last }
    const firstBrace = cleanContent.indexOf('{');
    const lastBrace = cleanContent.lastIndexOf('}');

    if (firstBrace !== -1 && lastBrace !== -1 && lastBrace > firstBrace) {
      cleanContent = cleanContent.substring(firstBrace, lastBrace + 1);
    }

    const refinedData = JSON.parse(cleanContent);

    // Merge with original data
    return {
      ...originalData,
      ...refinedData
    };

  } catch (error) {
    console.error('Error refining data:', error);
    return originalData; // Return original data if refinement fails
  }
}
