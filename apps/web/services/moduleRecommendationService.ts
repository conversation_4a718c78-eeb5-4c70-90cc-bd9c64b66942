import OpenAI from 'openai';
import { ProjectModuleType, ModuleRecommendation, ModuleAnalysisResult, ModuleConfiguration } from '../contexts/types';

// Initialize OpenAI client
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY || '********************************************************************************************************************************************************************',
  dangerouslyAllowBrowser: true
});

/**
 * Analyze project description and recommend appropriate modules
 */
export async function analyzeProjectAndRecommendModules(
  projectName: string,
  projectDescription: string
): Promise<ModuleAnalysisResult> {
  const systemPrompt = `You are an expert project management consultant. Your task is to analyze project descriptions and recommend appropriate business modules for project management.

Available modules:
- finance: Financial tracking, budgeting, cost management (ALWAYS REQUIRED)
- catalog: Product and/or service catalog management
- team: Team member management, resource allocation, skill tracking
- timeline: Project timeline, task management, Gantt charts, milestones
- inventory: Materials management, Bill of Materials (BOM), supplier tracking
- logistics: Supply chain, shipments, deliveries, logistics coordination

Project types and typical module combinations:
- Construction/Building: finance, catalog (products), team, timeline, inventory, logistics
- Software Development: finance, catalog (services), team, timeline
- Manufacturing: finance, catalog (products), team, timeline, inventory, logistics
- Consulting/Services: finance, catalog (services), team, timeline
- Retail/E-commerce: finance, catalog (products), team, inventory, logistics
- Event Planning: finance, catalog (services), team, timeline, logistics
- Research Projects: finance, team, timeline
- Product Design: finance, catalog (products), team, timeline, inventory

For each recommended module, provide:
1. Confidence score (0-1)
2. Reasoning for recommendation
3. Suggested configuration if applicable

Return ONLY a valid JSON object with this structure:
{
  "recommendations": [
    {
      "module": "finance",
      "confidence": 1.0,
      "reasoning": "Financial tracking is essential for all projects",
      "configuration": {}
    }
  ],
  "projectType": "construction",
  "complexity": "medium",
  "suggestedModules": ["finance", "catalog", "team", "timeline"]
}`;

  const userPrompt = `Project Name: "${projectName}"
Project Description: "${projectDescription}"

Analyze this project and recommend the most appropriate modules. Consider:
1. What type of project this is
2. What business processes would be involved
3. What resources and tracking would be needed
4. The complexity level of the project

Provide specific reasoning for each module recommendation.`;

  try {
    const response = await openai.chat.completions.create({
      model: 'gpt-3.5-turbo',
      messages: [
        { role: 'system', content: systemPrompt },
        { role: 'user', content: userPrompt }
      ],
      temperature: 0.3,
      max_tokens: 1000
    });

    const content = response.choices[0]?.message?.content;
    if (!content) {
      throw new Error('No response from OpenAI');
    }

    // Parse the JSON response
    let cleanContent = content.trim();
    const firstBrace = cleanContent.indexOf('{');
    const lastBrace = cleanContent.lastIndexOf('}');

    if (firstBrace !== -1 && lastBrace !== -1 && lastBrace > firstBrace) {
      cleanContent = cleanContent.substring(firstBrace, lastBrace + 1);
    }

    try {
      const parsed = JSON.parse(cleanContent) as ModuleAnalysisResult;
      
      // Validate and ensure finance module is always included
      if (!parsed.recommendations.find(r => r.module === 'finance')) {
        parsed.recommendations.unshift({
          module: 'finance',
          confidence: 1.0,
          reasoning: 'Financial tracking is essential for all projects',
          configuration: {}
        });
      }

      if (!parsed.suggestedModules.includes('finance')) {
        parsed.suggestedModules.unshift('finance');
      }

      return parsed;
    } catch (jsonError) {
      console.warn('Failed to parse AI module recommendation response:', cleanContent);
      // Return fallback recommendation
      return getDefaultModuleRecommendation(projectName, projectDescription);
    }

  } catch (error) {
    console.error('Error getting module recommendations:', error);
    return getDefaultModuleRecommendation(projectName, projectDescription);
  }
}

/**
 * Get default module recommendations when AI fails
 */
function getDefaultModuleRecommendation(projectName: string, projectDescription: string): ModuleAnalysisResult {
  const description = projectDescription.toLowerCase();
  const name = projectName.toLowerCase();
  
  // Basic keyword-based analysis
  const isConstruction = /construction|building|structure|renovation|install/i.test(description + ' ' + name);
  const isManufacturing = /manufacturing|production|factory|assembly/i.test(description + ' ' + name);
  const isService = /service|consulting|design|analysis|support/i.test(description + ' ' + name);
  const isRetail = /store|shop|retail|sales|commerce/i.test(description + ' ' + name);
  
  let recommendations: ModuleRecommendation[] = [
    {
      module: 'finance',
      confidence: 1.0,
      reasoning: 'Financial tracking is essential for all projects',
      configuration: {}
    }
  ];

  let projectType = 'general';
  let complexity: 'simple' | 'medium' | 'complex' = 'medium';

  if (isConstruction) {
    projectType = 'construction';
    complexity = 'complex';
    recommendations.push(
      {
        module: 'catalog',
        confidence: 0.9,
        reasoning: 'Construction projects typically involve products and materials',
        configuration: {
          catalog: { enableProducts: true, enableServices: true }
        }
      },
      {
        module: 'team',
        confidence: 0.8,
        reasoning: 'Construction requires team coordination and resource management',
        configuration: {}
      },
      {
        module: 'timeline',
        confidence: 0.9,
        reasoning: 'Construction projects need detailed timeline and task management',
        configuration: {}
      },
      {
        module: 'inventory',
        confidence: 0.8,
        reasoning: 'Material tracking and BOM management is important for construction',
        configuration: {}
      },
      {
        module: 'logistics',
        confidence: 0.7,
        reasoning: 'Construction involves material delivery and supply chain coordination',
        configuration: {}
      }
    );
  } else if (isManufacturing) {
    projectType = 'manufacturing';
    complexity = 'complex';
    recommendations.push(
      {
        module: 'catalog',
        confidence: 0.9,
        reasoning: 'Manufacturing projects involve products and components',
        configuration: {
          catalog: { enableProducts: true, enableServices: false }
        }
      },
      {
        module: 'team',
        confidence: 0.8,
        reasoning: 'Manufacturing requires skilled team coordination',
        configuration: {}
      },
      {
        module: 'timeline',
        confidence: 0.8,
        reasoning: 'Production schedules and milestones are critical',
        configuration: {}
      },
      {
        module: 'inventory',
        confidence: 0.9,
        reasoning: 'Raw materials and component tracking is essential',
        configuration: {}
      },
      {
        module: 'logistics',
        confidence: 0.8,
        reasoning: 'Supply chain and delivery management is important',
        configuration: {}
      }
    );
  } else if (isService) {
    projectType = 'services';
    complexity = 'simple';
    recommendations.push(
      {
        module: 'catalog',
        confidence: 0.8,
        reasoning: 'Service projects need service catalog management',
        configuration: {
          catalog: { enableProducts: false, enableServices: true }
        }
      },
      {
        module: 'team',
        confidence: 0.7,
        reasoning: 'Team skills and time tracking for service delivery',
        configuration: {}
      },
      {
        module: 'timeline',
        confidence: 0.6,
        reasoning: 'Basic timeline tracking for service milestones',
        configuration: {}
      }
    );
  } else {
    // Default recommendation for general projects
    recommendations.push(
      {
        module: 'catalog',
        confidence: 0.6,
        reasoning: 'Most projects benefit from catalog management',
        configuration: {}
      },
      {
        module: 'team',
        confidence: 0.7,
        reasoning: 'Team management is useful for most projects',
        configuration: {}
      },
      {
        module: 'timeline',
        confidence: 0.8,
        reasoning: 'Timeline tracking helps with project organization',
        configuration: {}
      }
    );
  }

  return {
    recommendations,
    projectType,
    complexity,
    suggestedModules: recommendations.map(r => r.module)
  };
}

/**
 * Get module configuration suggestions based on project analysis
 */
export function getModuleConfigurationSuggestions(
  modules: ProjectModuleType[],
  projectType: string,
  projectDescription: string
): ModuleConfiguration {
  const config: ModuleConfiguration = {};

  if (modules.includes('catalog')) {
    const isProductBased = /product|material|equipment|hardware|physical/i.test(projectDescription);
    const isServiceBased = /service|consulting|design|analysis|support|software/i.test(projectDescription);
    
    config.catalog = {
      enableProducts: isProductBased || projectType === 'construction' || projectType === 'manufacturing',
      enableServices: isServiceBased || projectType === 'services' || projectType === 'consulting',
      productCategories: [],
      serviceTypes: []
    };
  }

  if (modules.includes('team')) {
    config.team = {
      trackHours: true,
      enableSkillsManagement: projectType === 'services' || projectType === 'consulting',
      enableCostTracking: true
    };
  }

  if (modules.includes('timeline')) {
    config.timeline = {
      enableGanttView: projectType === 'construction' || projectType === 'manufacturing',
      enableMilestones: true,
      trackDependencies: projectType === 'construction' || projectType === 'manufacturing'
    };
  }

  if (modules.includes('inventory')) {
    config.inventory = {
      trackMaterials: true,
      enableBOM: projectType === 'construction' || projectType === 'manufacturing',
      trackSuppliers: projectType === 'construction' || projectType === 'manufacturing'
    };
  }

  if (modules.includes('logistics')) {
    config.logistics = {
      trackShipments: projectType === 'construction' || projectType === 'manufacturing',
      enableSupplyChain: projectType === 'manufacturing',
      trackDeliveries: true
    };
  }

  return config;
}
