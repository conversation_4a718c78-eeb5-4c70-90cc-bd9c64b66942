import { EntityType, ParsedIntent } from './openai';
import { 
  validateDeletion, 
  calculateFinancialImpact, 
  DeletionContext, 
  DeletionResult 
} from './deletionService';
import { 
  TeamMember, 
  Project, 
  InventoryItem, 
  FinancialRecord, 
  CatalogItem 
} from '../contexts/types';

// AI Deletion Response for conversational flow
export interface AIDeletionResponse {
  type: 'confirmation_needed' | 'deletion_complete' | 'error' | 'not_found' | 'multiple_matches';
  message: string;
  entity?: any;
  entities?: any[]; // For multiple matches
  warnings?: string[];
  errors?: string[];
  requiresConfirmation?: boolean;
  deletionContext?: DeletionContext;
  financialImpact?: {
    shouldCreateMovement: boolean;
    movementType?: 'income' | 'expense';
    amount?: number;
    description?: string;
  };
}

// Entity lookup functions type
export interface EntityLookupFunctions {
  findTeamMemberByName: (name: string) => TeamMember | null;
  findProjectByName: (name: string) => Project | null;
  findInventoryItemByName: (name: string) => InventoryItem | null;
  findFinancialRecordByDescription: (description: string) => FinancialRecord | null;
  findCatalogItemByName: (name: string) => CatalogItem | null;
  getTeamMemberById: (id: string) => TeamMember | undefined;
  getProjectById: (id: string) => Project | undefined;
  getInventoryItemById: (id: string) => InventoryItem | undefined;
  getFinancialRecordById: (id: string) => FinancialRecord | undefined;
  getCatalogItemById: (id: string) => CatalogItem | undefined;
  getAllTeamMembers: () => TeamMember[];
  getAllProjects: () => Project[];
  getAllInventoryItems: () => InventoryItem[];
  getAllFinancialRecords: () => FinancialRecord[];
  getAllCatalogItems: () => CatalogItem[];
}

// Entity deletion functions type
export interface EntityDeletionFunctions {
  removeTeamMember: (id: string) => TeamMember | null;
  removeProject: (id: string) => Project | null;
  removeInventoryItem: (id: string) => InventoryItem | null;
  removeFinancialRecord: (id: string) => FinancialRecord | null;
  removeCatalogItem: (id: string) => CatalogItem | null;
}

/**
 * Find entity based on extracted data from AI command
 */
export function findEntityFromCommand(
  entityType: EntityType,
  extractedData: Record<string, any>,
  lookupFunctions: EntityLookupFunctions
): any | null {
  // Try to find by ID first if provided
  if (extractedData.id) {
    switch (entityType) {
      case 'employee':
        return lookupFunctions.getTeamMemberById(extractedData.id);
      case 'project':
        return lookupFunctions.getProjectById(extractedData.id);
      case 'inventory':
        return lookupFunctions.getInventoryItemById(extractedData.id);
      case 'financial':
        return lookupFunctions.getFinancialRecordById(extractedData.id);
      case 'catalog':
        return lookupFunctions.getCatalogItemById(extractedData.id);
    }
  }

  // Try to find by name or description
  if (extractedData.name) {
    switch (entityType) {
      case 'employee':
        return lookupFunctions.findTeamMemberByName(extractedData.name);
      case 'project':
        return lookupFunctions.findProjectByName(extractedData.name);
      case 'inventory':
        return lookupFunctions.findInventoryItemByName(extractedData.name);
      case 'catalog':
        return lookupFunctions.findCatalogItemByName(extractedData.name);
    }
  }

  // For financial records, try description
  if (entityType === 'financial' && extractedData.description) {
    return lookupFunctions.findFinancialRecordByDescription(extractedData.description);
  }

  return null;
}

/**
 * Find multiple entities that match the search criteria
 */
export function findMultipleEntitiesFromCommand(
  entityType: EntityType,
  extractedData: Record<string, any>,
  lookupFunctions: EntityLookupFunctions & {
    getAllTeamMembers?: () => any[];
    getAllProjects?: () => any[];
    getAllInventoryItems?: () => any[];
    getAllFinancialRecords?: () => any[];
    getAllCatalogItems?: () => any[];
  }
): any[] {
  const searchTerm = extractedData.name || extractedData.description || '';
  const normalizedSearch = searchTerm.toLowerCase().trim();
  let allEntities: any[] = [];

  // Get all entities of the specified type
  switch (entityType) {
    case 'employee':
      allEntities = lookupFunctions.getAllTeamMembers?.() || [];
      break;
    case 'project':
      allEntities = lookupFunctions.getAllProjects?.() || [];
      break;
    case 'inventory':
      allEntities = lookupFunctions.getAllInventoryItems?.() || [];
      break;
    case 'financial':
      allEntities = lookupFunctions.getAllFinancialRecords?.() || [];
      break;
    case 'catalog':
      allEntities = lookupFunctions.getAllCatalogItems?.() || [];
      break;
  }

  // If no search term, return all entities (user wants to see all options)
  if (!normalizedSearch) return allEntities;

  // Filter entities that match the search term
  return allEntities.filter(entity => {
    const entityName = getEntityDisplayName(entity, entityType).toLowerCase();
    return entityName.includes(normalizedSearch) || normalizedSearch.includes(entityName);
  });
}

/**
 * Generate AI response for deletion request
 */
export async function generateDeletionResponse(
  intent: ParsedIntent,
  lookupFunctions: EntityLookupFunctions & {
    getAllTeamMembers?: () => any[];
    getAllProjects?: () => any[];
    getAllInventoryItems?: () => any[];
    getAllFinancialRecords?: () => any[];
    getAllCatalogItems?: () => any[];
  }
): Promise<AIDeletionResponse> {
  if (!intent.entityType) {
    return {
      type: 'error',
      message: 'I couldn\'t determine what type of item you want to delete. Please specify if you want to delete a project, employee, inventory item, catalog item, or financial record.',
    };
  }

  // Find the entity to delete
  const entity = findEntityFromCommand(intent.entityType, intent.extractedData, lookupFunctions);

  if (!entity) {
    // Try to find multiple matches
    const multipleEntities = findMultipleEntitiesFromCommand(intent.entityType, intent.extractedData, lookupFunctions);

    if (multipleEntities.length > 1) {
      const entityTypeName = getEntityTypeName(intent.entityType);
      const searchTerm = intent.extractedData.name || intent.extractedData.description || 'the specified item';

      let message = `I found multiple ${entityTypeName}s matching "${searchTerm}". Please choose which one you want to delete:\n\n`;

      multipleEntities.forEach((entity, index) => {
        const displayName = getEntityDisplayName(entity, intent.entityType!);
        const additionalInfo = getEntityAdditionalInfo(entity, intent.entityType!);
        message += `${index + 1}. ${displayName}${additionalInfo ? ` (${additionalInfo})` : ''}\n`;
      });

      message += '\nPlease specify which one you want to delete by saying something like "delete the first one" or "delete [specific name]".';

      return {
        type: 'multiple_matches',
        message,
        entities: multipleEntities,
      };
    }

    const entityTypeName = getEntityTypeName(intent.entityType);
    const searchTerm = intent.extractedData.name || intent.extractedData.description || 'the specified item';

    return {
      type: 'not_found',
      message: `I couldn't find a ${entityTypeName} matching "${searchTerm}". Please check the name and try again.`,
    };
  }

  // Validate if the entity can be deleted
  const validation = validateDeletion(intent.entityType, entity);

  if (!validation.canDelete) {
    return {
      type: 'error',
      message: `Cannot delete this ${getEntityTypeName(intent.entityType)}:\n${validation.errors.join('\n')}`,
      errors: validation.errors,
      entity,
    };
  }

  // Calculate financial impact for inventory items
  let financialImpact;
  if (intent.entityType === 'inventory') {
    financialImpact = calculateFinancialImpact(intent.entityType, entity as InventoryItem);
  }

  // Generate confirmation message
  const entityTypeName = getEntityTypeName(intent.entityType);
  const entityName = getEntityDisplayName(entity, intent.entityType);
  
  let message = `Are you sure you want to delete the ${entityTypeName} "${entityName}"?`;
  
  if (validation.warnings.length > 0) {
    message += '\n\nWarnings:\n' + validation.warnings.map(w => `• ${w}`).join('\n');
  }

  if (financialImpact?.shouldCreateMovement) {
    message += `\n\nThis will also create a financial movement: ${financialImpact.description} (${financialImpact.movementType === 'income' ? '+' : '-'}$${financialImpact.amount})`;
  }

  message += '\n\nType "confirm" to proceed or "cancel" to abort.';

  return {
    type: 'confirmation_needed',
    message,
    entity,
    warnings: validation.warnings,
    requiresConfirmation: true,
    financialImpact,
  };
}

/**
 * Execute the deletion after confirmation
 */
export async function executeDeletion(
  entityType: EntityType,
  entityId: string,
  deletionFunctions: EntityDeletionFunctions,
  context?: DeletionContext
): Promise<DeletionResult> {
  try {
    let deletedEntity: any = null;

    switch (entityType) {
      case 'employee':
        deletedEntity = deletionFunctions.removeTeamMember(entityId);
        break;
      case 'project':
        deletedEntity = deletionFunctions.removeProject(entityId);
        break;
      case 'inventory':
        deletedEntity = deletionFunctions.removeInventoryItem(entityId);
        break;
      case 'financial':
        deletedEntity = deletionFunctions.removeFinancialRecord(entityId);
        break;
      case 'catalog':
        deletedEntity = deletionFunctions.removeCatalogItem(entityId);
        break;
      default:
        return {
          success: false,
          error: `Unsupported entity type: ${entityType}`,
        };
    }

    if (!deletedEntity) {
      return {
        success: false,
        error: 'Entity not found or could not be deleted',
      };
    }

    // Calculate financial impact for inventory items
    let financialImpact;
    if (entityType === 'inventory') {
      financialImpact = calculateFinancialImpact(entityType, deletedEntity as InventoryItem, context);
    }

    return {
      success: true,
      deletedEntity,
      financialImpact,
    };
  } catch (error) {
    console.error('Error executing deletion:', error);
    return {
      success: false,
      error: 'An error occurred while deleting the entity',
    };
  }
}

/**
 * Helper function to get user-friendly entity type name
 */
function getEntityTypeName(entityType: EntityType): string {
  switch (entityType) {
    case 'employee':
      return 'employee';
    case 'project':
      return 'project';
    case 'inventory':
      return 'inventory item';
    case 'financial':
      return 'financial record';
    case 'catalog':
      return 'catalog item';
    default:
      return 'item';
  }
}

/**
 * Helper function to get display name for an entity
 */
function getEntityDisplayName(entity: any, entityType: EntityType): string {
  switch (entityType) {
    case 'employee':
      return entity.name || entity.id;
    case 'project':
      return entity.name || entity.id;
    case 'inventory':
      return entity.name || entity.id;
    case 'financial':
      return entity.description || entity.id;
    case 'catalog':
      return entity.productName || entity.id;
    default:
      return entity.id || 'Unknown';
  }
}

/**
 * Helper function to get additional info for an entity (for disambiguation)
 */
function getEntityAdditionalInfo(entity: any, entityType: EntityType): string {
  switch (entityType) {
    case 'employee':
      return entity.role || entity.department || '';
    case 'project':
      return entity.client || entity.status || '';
    case 'inventory':
      return entity.category || entity.quantity ? `Qty: ${entity.quantity}` : '';
    case 'financial':
      return entity.amount ? `$${entity.amount}` : entity.date || '';
    case 'catalog':
      return entity.categoryLabel || '';
    default:
      return '';
  }
}
