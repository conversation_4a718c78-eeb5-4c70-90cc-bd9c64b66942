"use client";

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { CatalogContextState, CatalogItem } from './types';
import { mockCatalogItems } from './mockData';

interface CatalogContextActions {
  selectItem: (item: CatalogItem | null) => void;
  getItemById: (id: string) => CatalogItem | undefined;
  refreshItems: () => Promise<void>;
  addCatalogItem: (item: CatalogItem) => void;
  removeCatalogItem: (id: string) => CatalogItem | null;
  findCatalogItemByName: (name: string) => CatalogItem | null;
}

type CatalogContextType = CatalogContextState & CatalogContextActions;

const CatalogContext = createContext<CatalogContextType | undefined>(undefined);

interface CatalogProviderProps {
  children: ReactNode;
}

export const CatalogProvider: React.FC<CatalogProviderProps> = ({ children }) => {
  const [state, setState] = useState<CatalogContextState>({
    items: [],
    selectedItem: null,
    loading: true,
    error: null,
  });

  // Simulate API call to fetch catalog items
  const fetchCatalogItems = async (): Promise<CatalogItem[]> => {
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // In a real implementation, this would be an API call
    // return await api.getCatalogItems();
    return mockCatalogItems;
  };

  const refreshItems = async () => {
    setState(prev => ({ ...prev, loading: true, error: null }));
    
    try {
      const items = await fetchCatalogItems();
      setState(prev => ({
        ...prev,
        items,
        loading: false,
      }));
    } catch (error) {
      setState(prev => ({
        ...prev,
        loading: false,
        error: error instanceof Error ? error.message : 'Failed to fetch catalog items',
      }));
    }
  };

  const selectItem = (item: CatalogItem | null) => {
    setState(prev => ({
      ...prev,
      selectedItem: item,
    }));
  };

  const getItemById = (id: string): CatalogItem | undefined => {
    return state.items.find(item => item.id === id);
  };

  const addCatalogItem = (item: CatalogItem) => {
    setState(prev => ({
      ...prev,
      items: [...prev.items, item],
    }));
  };

  const removeCatalogItem = (id: string): CatalogItem | null => {
    let removedItem: CatalogItem | null = null;

    setState(prev => {
      const itemToRemove = prev.items.find(item => item.id === id);
      if (itemToRemove) {
        removedItem = itemToRemove;

        // If the removed item is currently selected, clear the selection
        if (prev.selectedItem?.id === id) {
          return {
            ...prev,
            items: prev.items.filter(item => item.id !== id),
            selectedItem: null,
          };
        }

        return {
          ...prev,
          items: prev.items.filter(item => item.id !== id),
        };
      }
      return prev;
    });

    return removedItem;
  };

  const findCatalogItemByName = (name: string): CatalogItem | null => {
    if (!name || !state.items.length) return null;

    const normalizedSearch = name.toLowerCase().trim();

    // First try exact match
    let found = state.items.find(item =>
      item.productName.toLowerCase() === normalizedSearch
    );

    if (found) return found;

    // Then try partial match
    found = state.items.find(item =>
      item.productName.toLowerCase().includes(normalizedSearch) ||
      normalizedSearch.includes(item.productName.toLowerCase())
    );

    return found || null;
  };

  // Load initial data
  useEffect(() => {
    refreshItems();
  }, []);

  const contextValue: CatalogContextType = {
    ...state,
    selectItem,
    getItemById,
    refreshItems,
    addCatalogItem,
    removeCatalogItem,
    findCatalogItemByName,
  };

  return (
    <CatalogContext.Provider value={contextValue}>
      {children}
    </CatalogContext.Provider>
  );
};

export const useCatalog = (): CatalogContextType => {
  const context = useContext(CatalogContext);
  if (context === undefined) {
    throw new Error('useCatalog must be used within a CatalogProvider');
  }
  return context;
};
