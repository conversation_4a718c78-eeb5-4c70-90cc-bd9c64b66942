"use client";

import React, { createContext, useContext, useState, useEffect, useCallback, ReactNode } from 'react';
import { FinanceContextState, FinancialRecord, FinancialMovement, ForecastEntry } from './types';
import { mockFinancialRecords } from './mockData';
import { generateForecastsFromMovement, updateForecastsForPayment, removeForecastsBySource } from '../services/forecastService';

interface FinanceContextActions {
  getRecordById: (id: string) => FinancialRecord | undefined;
  getMovementById: (id: string) => FinancialMovement | undefined;
  refreshRecords: () => Promise<void>;
  addFinancialRecord: (record: FinancialRecord) => void;
  addFinancialMovement: (movement: FinancialMovement) => void;
  getRecordsByType: (type: 'income' | 'expense') => FinancialRecord[];
  getMovementsByType: (type: 'Entrada' | 'Salida') => FinancialMovement[];
  getRecordsByCategory: (category: string) => FinancialRecord[];
  getTotalByType: (type: 'income' | 'expense') => number;
  // Forecast management
  addForecastEntries: (forecasts: ForecastEntry[]) => void;
  getForecastsForMonth: (year: number, month: number) => ForecastEntry[];
  updateForecastConfirmation: (forecastId: string, isConfirmed: boolean) => void;
  removeForecastsBySourceId: (sourceId: string) => void;
  removeFinancialRecord: (id: string) => FinancialRecord | null;
  removeFinancialMovement: (id: string) => FinancialMovement | null;
  findFinancialRecordByDescription: (description: string) => FinancialRecord | null;
}

type FinanceContextType = FinanceContextState & FinanceContextActions;

const FinanceContext = createContext<FinanceContextType | undefined>(undefined);

interface FinanceProviderProps {
  children: ReactNode;
}

// Convert FinancialRecord to FinancialMovement for display
function convertRecordToMovement(record: FinancialRecord): FinancialMovement {
  return {
    id: record.id,
    fecha: record.date,
    concepto: record.description,
    monto: record.amount,
    tipo: record.type === 'income' ? 'Entrada' : 'Salida',
    categoria: record.category,
    comportamiento: 'Fijo', // Default behavior
    comprobante: '🗂', // Default document icon
    paymentMethod: 'cash', // Default payment method
    isRecurring: false, // Default not recurring
    recurringDetails: undefined,
    dueDate: undefined
  };
}

export const FinanceProvider: React.FC<FinanceProviderProps> = ({ children }) => {
  const [state, setState] = useState<FinanceContextState>({
    financialRecords: [],
    movements: [],
    forecasts: [],
    loading: true,
    error: null,
  });

  // Simulate API call to fetch financial records
  const fetchFinancialRecords = async (): Promise<FinancialRecord[]> => {
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 300));
    
    // In a real implementation, this would be an API call
    // return await api.getFinancialRecords();
    return mockFinancialRecords;
  };

  const refreshRecords = useCallback(async () => {
    try {
      setState(prev => ({ ...prev, loading: true, error: null }));
      const records = await fetchFinancialRecords();
      
      // Convert records to movements for display
      const movements = records.map(convertRecordToMovement);
      
      setState(prev => ({
        ...prev,
        financialRecords: records,
        movements: movements,
        forecasts: prev.forecasts, // Keep existing forecasts
        loading: false
      }));
    } catch (error) {
      setState(prev => ({
        ...prev,
        loading: false,
        error: error instanceof Error ? error.message : 'Failed to fetch financial records'
      }));
    }
  }, []);

  const getRecordById = useCallback((id: string): FinancialRecord | undefined => {
    return state.financialRecords.find(record => record.id === id);
  }, [state.financialRecords]);

  const getMovementById = useCallback((id: string): FinancialMovement | undefined => {
    return state.movements.find(movement => movement.id === id);
  }, [state.movements]);

  const addFinancialRecord = useCallback((record: FinancialRecord) => {
    const movement = convertRecordToMovement(record);
    
    setState(prev => ({
      ...prev,
      financialRecords: [...prev.financialRecords, record],
      movements: [...prev.movements, movement],
    }));
  }, []);

  const addFinancialMovement = useCallback((movement: FinancialMovement) => {
    // Generate forecasts if this is a recurring payment
    const forecasts = generateForecastsFromMovement(movement);

    setState(prev => ({
      ...prev,
      movements: [...prev.movements, movement],
      forecasts: [...prev.forecasts, ...forecasts],
    }));
  }, []);

  const getRecordsByType = useCallback((type: 'income' | 'expense'): FinancialRecord[] => {
    return state.financialRecords.filter(record => record.type === type);
  }, [state.financialRecords]);

  const getMovementsByType = useCallback((type: 'Entrada' | 'Salida'): FinancialMovement[] => {
    return state.movements.filter(movement => movement.tipo === type);
  }, [state.movements]);

  const getRecordsByCategory = useCallback((category: string): FinancialRecord[] => {
    return state.financialRecords.filter(record => record.category === category);
  }, [state.financialRecords]);

  const getTotalByType = useCallback((type: 'income' | 'expense'): number => {
    return state.financialRecords
      .filter(record => record.type === type)
      .reduce((total, record) => total + record.amount, 0);
  }, [state.financialRecords]);

  // Forecast management functions
  const addForecastEntries = useCallback((forecasts: ForecastEntry[]) => {
    setState(prev => ({
      ...prev,
      forecasts: [...prev.forecasts, ...forecasts],
    }));
  }, []);

  const getForecastsForMonth = useCallback((year: number, month: number): ForecastEntry[] => {
    const monthStr = month.toString().padStart(2, '0');
    const yearMonthPrefix = `${year}-${monthStr}`;

    return state.forecasts.filter(forecast =>
      forecast.date.startsWith(yearMonthPrefix)
    );
  }, [state.forecasts]);

  const updateForecastConfirmation = useCallback((forecastId: string, isConfirmed: boolean) => {
    setState(prev => ({
      ...prev,
      forecasts: prev.forecasts.map(forecast =>
        forecast.id === forecastId ? { ...forecast, isConfirmed } : forecast
      ),
    }));
  }, []);

  const removeForecastsBySourceId = useCallback((sourceId: string) => {
    setState(prev => ({
      ...prev,
      forecasts: removeForecastsBySource(prev.forecasts, sourceId),
    }));
  }, []);

  const removeFinancialRecord = useCallback((id: string): FinancialRecord | null => {
    let removedRecord: FinancialRecord | null = null;

    setState(prev => {
      const recordToRemove = prev.financialRecords.find(record => record.id === id);
      if (recordToRemove) {
        removedRecord = recordToRemove;
        return {
          ...prev,
          financialRecords: prev.financialRecords.filter(record => record.id !== id),
        };
      }
      return prev;
    });

    return removedRecord;
  }, []);

  const removeFinancialMovement = useCallback((id: string): FinancialMovement | null => {
    let removedMovement: FinancialMovement | null = null;

    setState(prev => {
      const movementToRemove = prev.movements.find(movement => movement.id === id);
      if (movementToRemove) {
        removedMovement = movementToRemove;
        return {
          ...prev,
          movements: prev.movements.filter(movement => movement.id !== id),
        };
      }
      return prev;
    });

    return removedMovement;
  }, []);

  const findFinancialRecordByDescription = useCallback((description: string): FinancialRecord | null => {
    if (!description || !state.financialRecords.length) return null;

    const normalizedSearch = description.toLowerCase().trim();

    // First try exact match
    let found = state.financialRecords.find(record =>
      record.description.toLowerCase() === normalizedSearch
    );

    if (found) return found;

    // Then try partial match
    found = state.financialRecords.find(record =>
      record.description.toLowerCase().includes(normalizedSearch) ||
      normalizedSearch.includes(record.description.toLowerCase())
    );

    return found || null;
  }, [state.financialRecords]);

  // Load initial data
  useEffect(() => {
    refreshRecords();
  }, [refreshRecords]);

  const contextValue: FinanceContextType = {
    ...state,
    getRecordById,
    getMovementById,
    refreshRecords,
    addFinancialRecord,
    addFinancialMovement,
    getRecordsByType,
    getMovementsByType,
    getRecordsByCategory,
    getTotalByType,
    addForecastEntries,
    getForecastsForMonth,
    updateForecastConfirmation,
    removeForecastsBySourceId,
    removeFinancialRecord,
    removeFinancialMovement,
    findFinancialRecordByDescription,
  };

  return (
    <FinanceContext.Provider value={contextValue}>
      {children}
    </FinanceContext.Provider>
  );
};

export const useFinance = (): FinanceContextType => {
  const context = useContext(FinanceContext);
  if (context === undefined) {
    throw new Error('useFinance must be used within a FinanceProvider');
  }
  return context;
};
