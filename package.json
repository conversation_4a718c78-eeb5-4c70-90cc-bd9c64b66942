{"name": "admin-monorepo", "private": true, "scripts": {"build": "turbo run build", "dev": "turbo run dev", "lint": "turbo run lint", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "check-types": "turbo run check-types", "storybook": "turbo run storybook", "build-storybook": "turbo run build-storybook"}, "devDependencies": {"prettier": "^3.5.3", "turbo": "^2.5.4", "typescript": "5.8.2"}, "engines": {"node": ">=18"}, "packageManager": "npm@11.4.0", "workspaces": ["apps/*", "packages/*"], "dependencies": {"openai": "^5.6.0"}}